import org.gradle.api.internal.artifacts.dsl.dependencies.DependenciesExtensionModule.module
import org.gradle.api.tasks.testing.logging.TestExceptionFormat
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    java
    `maven-publish`
    val kotlinVersion = "2.0.20"
    kotlin("jvm") version kotlinVersion apply true
    kotlin("kapt") version kotlinVersion apply true
    id("com.google.cloud.artifactregistry.gradle-plugin") version "2.2.5" apply false
    id("io.vertx.vertx-plugin") version "1.4.0" apply false
    id("org.flywaydb.flyway") version "11.9.1" apply false
    id("com.github.johnrengelman.shadow") version "8.1.1" apply false
    id("com.google.devtools.ksp") version "${kotlinVersion}-1.0.25" apply false
    idea
}

object Versions {
    val kotest = "5.3.1"
    val kotestx = "1.0.3"
    val mockk = "1.13.3"
    val lombok = "1.18.24"
}

repositories {
    mavenLocal()
    mavenCentral()
}

subprojects {
    val project = this

    if (project.name == "modules") return@subprojects

    /**
     * The jar-file version. All jar files will be published using this version. This means that
     * if we happen to have multiple jar files published to Artifact Registry, all of them will
     * have the same version. This not only makes the deployment simpler, but also makes the
     * lives of those using Dapi jars easier.
     *
     * We use Semantic Versioning for our jar files:
     *  - if you're just fixing an issue, not breaking backward-compatibilities, nor introducing
     *    new features, then please only bump the patch version. (e.g. going from 1.0.0 to 1.0.1)
     *  - if you're is introducing a new feature but is not breaking backward-compatibilities,
     *    then please update the minor version (e.g. going from 1.0.0 to 1.1.0)
     *  - if your patch changes make it incompatible with the current version (e.g. adding
     *    non-nullable fields, renaming classes/packages, deleting fields or classes, etc)
     *    then please bump the major version. (e.g. going from 1.0.0 to 2.0.0)
     */
    version = "5.6.0"

    /**
     * The groupId used when the artifact is published.
     */
    group = "nct.engineering.dapi"

    /**
     * Plugins that will be used by all sub-projects.
     */
    apply {
        plugin("java-library")
        plugin("org.jetbrains.kotlin.jvm")
        plugin("org.jetbrains.kotlin.kapt")
        plugin("com.google.cloud.artifactregistry.gradle-plugin")
        plugin("idea")
    }

    /**
     * Repositories that will be used by all sub-projects.
     */
    repositories {
        mavenLocal()
        mavenCentral()
        maven {
            url = uri("artifactregistry://australia-southeast1-maven.pkg.dev/upside-ci/nct-maven-snapshots")
        }
        maven {
            url = uri("artifactregistry://australia-southeast1-maven.pkg.dev/upside-ci/nct-maven-release")
        }
    }

    /**
     * Customise, simplified source structure.
     */
    sourceSets {
        main {
            java {
                srcDirs("source")
            }
            resources {
                srcDirs("resources")
            }
        }
        test {
            java {
                srcDirs("tests/source")
            }
            resources {
                srcDirs("tests/resources")
            }
        }
    }

    sourceSets {
        create("integration-tests") {
            java.srcDir("integration-tests")
            compileClasspath += main.get().output + test.get().output
            runtimeClasspath += main.get().output + test.get().output
        }
    }

    // depends on the same dependencies as tests
    val integrationTestsImplementation by configurations.getting {
        extendsFrom(configurations.testImplementation.get())
    }

    val integrationTest = task<Test>("integration-tests") {
        description = "Runs integration tests."
        group = "verification"

        testClassesDirs = sourceSets["integration-tests"].output.classesDirs
        classpath = sourceSets["integration-tests"].runtimeClasspath
        shouldRunAfter("test")
    }

    idea {
        module {
            testSources.from(sourceSets["integration-tests"].java.srcDirs)
        }
    }

    java {
        sourceCompatibility = JavaVersion.VERSION_17
    }

    tasks.withType<KotlinCompile> {
        kotlinOptions {
            freeCompilerArgs = listOf("-Xjsr305=strict")
            jvmTarget = "17"
        }
    }

    tasks.withType<Test> {
        useJUnitPlatform()

        testLogging {
            events("PASSED", "SKIPPED", "FAILED", "STANDARD_ERROR")
            showStandardStreams = true
            showCauses = true
            showExceptions = true
            showStackTraces = true
            exceptionFormat = TestExceptionFormat.FULL
        }
    }

    /**
     * Common dependencies.
     */
    dependencies {
        // Lombok
        compileOnly("org.projectlombok:lombok:${Versions.lombok}")
        kapt("org.projectlombok:lombok:${Versions.lombok}")

        // test configuration
        testImplementation("io.kotest:kotest-assertions-core-jvm:${Versions.kotest}")
        testImplementation("io.kotest:kotest-framework-engine-jvm:${Versions.kotest}")
        testImplementation("io.kotest:kotest-runner-junit5-jvm:${Versions.kotest}")
        testImplementation("io.kotest.extensions:kotest-extensions-wiremock:${Versions.kotestx}")
        testImplementation("io.mockk:mockk:${Versions.mockk}")
    }

    /**
     * Rules specific for jar files. At this point in time, as
     * the `app` module is the only non-jar deployable, we hardcoded
     * the logic below. Please revisit this when this cease to be
     * the case.
     */
    if (project.name != "app") {
        apply { plugin("maven-publish") }

        // Create a source JAR for publishing
        java {
            withSourcesJar()
        }

        publishing {
            repositories {
                maven {
                    url = uri("artifactregistry://australia-southeast1-maven.pkg.dev/upside-ci/nct-maven-release")
                }
            }
            publications {
                create<MavenPublication>("maven") {
                    groupId = project.group.toString()
                    version = project.version.toString()
                    artifactId = "nct-dapi-${project.name}"
                    from(components["java"])

                    pom {
                        scm {
                            connection.set("scm:git:**************:UpsideRealty/nct-public-api.git")
                            developerConnection.set("scm:git:**************:UpsideRealty/nct-public-api.git")
                            url.set("https://github.com/UpsideRealty/nct-public-api")
                        }
                    }
                }
            }
        }
    }
}
