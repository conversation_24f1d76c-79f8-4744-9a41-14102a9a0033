version: 2
registries:
  terraform-io:
    type: terraform-registry
    url: https://app.terraform.io
    token: ${{ secrets.HCP_TERRAFORM_API_TOKEN }}
updates:
  - package-ecosystem: "gradle"
    directory: "/"
    schedule:
      interval: "weekly"
  - package-ecosystem: "terraform"
    directories:
      - "current-new-infrastructure/source"
      - "current-new-infrastructure/source_db-config"
    registries:
      - terraform-io
    schedule:
      interval: "daily"
    groups:
      terraform-current-new-infrastructure:
        patterns:
          - "*"
