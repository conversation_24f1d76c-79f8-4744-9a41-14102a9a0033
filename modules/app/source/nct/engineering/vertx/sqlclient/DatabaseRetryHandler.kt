package nct.engineering.vertx.sqlclient

import com.google.common.annotations.VisibleForTesting
import nct.engineering.modern.commons.future.EmptyFuture
import nct.engineering.modern.commons.future.NotNullFuture
import nct.engineering.modern.commons.future.NullableFuture
import nct.engineering.modern.commons.future.exceptionWrapper
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.sql.SQLException

/**
 * Utility class for handling database operation retries, specifically for deadlock scenarios.
 *
 * This component provides retry logic for database operations that may fail due to deadlocks
 * or serialization failures.
 */
class DatabaseRetryHandler(
    private val logger: Logger = LoggerFactory.getLogger(DatabaseRetryHandler::class.java),
    private val maxRetries: Int = DEFAULT_MAX_RETRIES
) {

    /**
     * Retries an operation that returns an EmptyFuture on deadlock exceptions.
     */
    fun retryOnDeadlock(operation: () -> EmptyFuture): EmptyFuture =
        retryOnDeadlock(operation, 0)

    private fun retryOnDeadlock(
        operation: () -> EmptyFuture,
        attempt: Int
    ): EmptyFuture {
        return operation().recover { exception ->
            when {
                attempt < maxRetries && isDeadlockException(exception) -> {
                    logger.warn("Deadlock detected on attempt ${attempt + 1}/${maxRetries + 1}", exception)
                    retryOnDeadlock(operation, attempt + 1)
                }
                else -> exceptionWrapper().wrapAsEmptyFuture(exception)
            }
        }
    }

    /**
     * Determines if an exception is related to a database deadlock or serialization failure.
     *
     * This method checks for various PostgreSQL error codes and messages that indicate
     * deadlock or serialization failure conditions:
     * - 40001: serialization_failure
     * - 40P01: deadlock_detected
     * - Various message patterns indicating deadlock or serialization issues
     */
    @VisibleForTesting
    fun isDeadlockException(exception: Throwable): Boolean {
        var current: Throwable? = exception
        while (current != null) {
            val message = current.message?.lowercase() ?: ""

            if (message.contains("40001") || // serialization_failure
                message.contains("40p01") || // deadlock_detected
                message.contains("deadlock") ||
                message.contains("serialization failure") ||
                message.contains("could not serialize access")
            ) {
                return true
            }

            // Check for SQLException with specific SQL states
            if (current is SQLException) {
                val sqlState = current.sqlState?.lowercase()
                if (sqlState == "40001" || sqlState == "40p01") {
                    return true
                }
            }

            current = current.cause
        }
        return false
    }

    companion object {
        @VisibleForTesting
        const val DEFAULT_MAX_RETRIES = 10
    }
}
