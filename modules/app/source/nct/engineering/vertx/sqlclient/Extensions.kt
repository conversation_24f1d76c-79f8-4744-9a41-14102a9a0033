package nct.engineering.vertx.sqlclient

import io.vertx.core.Future
import io.vertx.micrometer.backends.BackendRegistries
import io.vertx.pgclient.PgPool
import io.vertx.sqlclient.Row
import io.vertx.sqlclient.RowSet
import io.vertx.sqlclient.SqlClient
import io.vertx.sqlclient.SqlConnection
import io.vertx.sqlclient.TransactionRollbackException
import io.vertx.sqlclient.Tuple
import nct.cloud.observe.SimpleCounter
import nct.engineering.modern.commons.future.EmptyFuture
import nct.engineering.modern.commons.future.NctFutures
import nct.engineering.modern.commons.future.NctFutures.join
import nct.engineering.modern.commons.future.NotNullFuture
import nct.engineering.modern.commons.future.NullableFuture
import nct.engineering.modern.commons.future.exceptionWrapper
import org.slf4j.LoggerFactory
import org.slf4j.impl.GCPAwareMDC
import java.sql.SQLException
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicInteger

private val logger = LoggerFactory.getLogger("nct.engineering.vertx.sqlclient")

object SimplePostgresClientConfiguration {
    var defaultTransactionIsolationLevel = TransactionIsolationLevel.Serializable
}

enum class TransactionIsolationLevel(val value: String) {
    Serializable("SERIALIZABLE"),
    RepeatableRead("REPEATABLE READ"),
    ReadCommitted("READ COMMITTED"),
    ReadUncommitted("READ UNCOMMITTED"),
}

fun<T> PgPool.transaction(handler: SqlConnection.() -> NullableFuture<T>): NullableFuture<T> =
    transaction(SimplePostgresClientConfiguration.defaultTransactionIsolationLevel, handler)

fun<T> PgPool.transaction(transactionIsolationLevel: TransactionIsolationLevel, handler: SqlConnection.() -> NullableFuture<T>): NullableFuture<T> {
    val dump = GCPAwareMDC.dump()
    val wrapper = exceptionWrapper()
    val haveBeenAbleToHoldAConnection = AtomicBoolean(false)
    return safelyHandleTransaction { sql ->
            GCPAwareMDC.load(dump)
            haveBeenAbleToHoldAConnection.set(true)
            sql.executeQuery("SET TRANSACTION ISOLATION LEVEL ${transactionIsolationLevel.value}")
                .onFailure { logger.error("Failed to set the transaction level", it) }
                .composeAsNullable { handler(sql) }
        }
        .let { NctFutures.wrapAsNullable(it) }
        .recover {
            val wrapped = SQLException("Transaction failed: haveBeenAbleToHoldAConnection=$haveBeenAbleToHoldAConnection", it)
            wrapper.wrapAsNullableFuture(wrapped)
        }
}

val currentlyOpenedConnections = AtomicInteger(0)

fun<T> PgPool.safelyHandleTransaction(fn: (SqlConnection) -> NullableFuture<T>): NullableFuture<T> {
    return connection.compose { conn ->
        conn.begin().compose { transaction ->
            currentlyOpenedConnections.incrementAndGet()

            try {
                fn.invoke(conn)
            } catch (cause: Throwable) {
                Future.failedFuture(cause)
            }
            .compose {
                transaction.commit().map(it)
            }
            .recover {
                var failure = Future.failedFuture<T>(it)
                if (it is TransactionRollbackException)
                    failure = transaction.rollback().compose { failure }
                failure
            }
        }
        .compose {
            conn.close()
                .onComplete {
                    val currentlyActiveConns = currentlyOpenedConnections.decrementAndGet()
                    logger.info("Connection closed. There are $currentlyActiveConns live connections that are still opened")
                }
                .map(it)
        }
    }
    .let { NctFutures.wrapAsNullable(it) }
}

fun SqlClient.executeQuery(sql: String): NotNullFuture<RowSet<Row>> {
    logger.debug("Executing query: $sql")
    return query(sql)
        .execute()
        .recover { it.handleSqlFailure(sql) }
        .let(NctFutures::wrapAsNotNull)
}

fun SqlClient.executeBatch(sql: String, values: List<Tuple>): NotNullFuture<RowSet<Row>> {
    logger.debug("Executing batch: {}. Params: {}", sql, values)
    return preparedQuery(sql.replace("\n", " "))
        .executeBatch(values)
        .recover { it.handleSqlFailure(sql) }
        .let(NctFutures::wrapAsNotNull)
}

private fun<T: Any> Throwable.handleSqlFailure(sql: String): NotNullFuture<T> {
    val msg = "Failed to execute query: $sql"
    logger.error(msg, this)
    return NctFutures.failedFuture(msg, SQLException(msg, this))
}

fun SqlClient.parallelExecuteQueries(vararg sql: String): EmptyFuture =
    listOf(*sql)
        .map { this.executeQuery(it) }
        .join()

fun<T: Any> Row.getOrNull(clazz: Class<T>, name: String): T? = try {
    get(clazz, name)
} catch (cause: NoSuchElementException) {
    null
}

fun Row.getLongOrNull(column: String): Long? = try {
    this.getLong(column)
} catch (npe: NullPointerException) {
    null
}
