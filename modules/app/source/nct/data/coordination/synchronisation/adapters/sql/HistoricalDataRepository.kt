package nct.data.coordination.synchronisation.adapters.sql

import com.fasterxml.jackson.databind.ObjectMapper
import com.google.common.annotations.VisibleForTesting
import injector.Singleton
import io.vertx.sqlclient.Tuple
import nct.data.coordination.synchronisation.models.HistoricalData
import nct.data.coordination.synchronisation.models.SyncAllHistoricalData
import nct.data.coordination.synchronisation.models.SyncHistoricalDataById
import nct.data.schemas.commons.ComponentModel
import nct.data.schemas.commons.DeleteComponentModel
import nct.data.schemas.commons.Identifiable
import nct.data.schemas.commons.NotificationEnvelope
import nct.data.schemas.commons.PayloadState
import nct.engineering.modern.commons.future.EmptyFuture
import nct.engineering.modern.commons.future.NctFutures.failedNullableFuture
import nct.engineering.modern.commons.future.NctFutures.join
import nct.engineering.modern.commons.future.NctFutures.succeededEmptyFuture
import nct.engineering.modern.commons.future.NctFutures.succeededNullableFuture
import nct.engineering.modern.commons.future.NotNullFuture
import nct.engineering.modern.commons.future.NullableFuture
import nct.engineering.modern.pg.PostgresClient
import nct.engineering.modern.pg.PostgresConnection
import nct.engineering.modern.pg.mapper.JsonSerialisedObjectWrapper
import nct.engineering.vertx.sqlclient.DatabaseRetryHandler
import org.slf4j.LoggerFactory
import java.util.*

private val EMPTY_UUID = UUID.fromString("00000000-0000-0000-0000-000000000000")

@Singleton
class HistoricalDataRepository(
    private val db: PostgresClient,
    private val objectMapper: ObjectMapper,
) {

    private val logger = LoggerFactory.getLogger(javaClass)
    private val retryHandler = DatabaseRetryHandler(logger)

    private fun upsertQuery(batchSize: Int) = """
        INSERT INTO data_api_v2.historical_data
            (org_name, business_id, agency_id, id, component_name,
             payload, is_deleted, last_touched, delete_is_merge, merge_payload)
        VALUES ${(1..(batchSize * NUM_ROW_PLACEHOLDERS) step NUM_ROW_PLACEHOLDERS).joinToString(",") { i -> "(\$${i}, \$${i + 1}, \$${i + 2}, \$${i + 3}, \$${i + 4}, \$${i + 5}, \$${i + 6}, CURRENT_TIMESTAMP, \$${i + 7}, \$${i + 8}) " }}
        ON CONFLICT (component_name, org_name, id) DO UPDATE SET
            business_id = EXCLUDED.business_id,
            agency_id = EXCLUDED.agency_id,
            is_deleted = EXCLUDED.is_deleted,
            payload = EXCLUDED.payload,
            last_touched = CURRENT_TIMESTAMP,
            delete_is_merge = EXCLUDED.delete_is_merge,
            merge_payload = EXCLUDED.merge_payload
    """

    private val deleteQueryWithoutMerge = """
        UPDATE data_api_v2.historical_data
           SET is_deleted = true,
               last_touched = CURRENT_TIMESTAMP,
               delete_is_merge = false
         WHERE component_name = $1
           AND org_name = $2
           AND id = ANY($3)
    """.trimIndent()

    private val deleteQueryForMerges = """
        UPDATE data_api_v2.historical_data
           SET is_deleted = true,
               last_touched = CURRENT_TIMESTAMP,
               delete_is_merge = $4,
               merge_payload = $5
         WHERE component_name = $1
           AND org_name = $2
           AND id = $3
    """.trimIndent()

    private fun retrievePageOfDataQuery(deletedOnly: Boolean) = """
        SELECT * FROM data_api_v2.historical_data
         WHERE component_name = $1
           AND org_name = $2
           AND business_id = $3
           AND agency_id = $4
           AND row_number > $5
           AND last_touched >= $6
           ${if (deletedOnly) "AND is_deleted = true" else ""}
         ORDER BY row_number ASC
         LIMIT 100
    """.trimIndent()

    private val retrieveSingleEntryByIdQuery = """
        SELECT * FROM data_api_v2.historical_data
         WHERE component_name = $1
           AND org_name = $2
           AND business_id = $3
           AND agency_id = $4
           AND id = $5
    """.trimIndent()

    /**
     * Ingests the received modifications from the SoT and reflects
     * them into the main database.
     */
    fun ingest(event: NotificationEnvelope): EmptyFuture =
        when (event.payload.size) {
            0 -> succeededEmptyFuture()
            else -> when (event.state) {
                PayloadState.Current,
                PayloadState.Created,
                PayloadState.Updated -> upsert(event)
                PayloadState.CurrentDeleted,
                PayloadState.Deleted -> delete(event)
                PayloadState.Receipt -> succeededEmptyFuture()
            }
        }

    private fun upsert(event: NotificationEnvelope) =
        retryHandler.retryOnDeadlock {
            db.withTransactionAndReturnEmpty { conn ->
                conn.executePreparedQuery(
                    sql = upsertQuery(event.payload.size),
                    values = listOf(event.toHistoricalDataEntities().toFlatTuple()),
                ).mapAsEmptyFuture()
            }
        }

    private fun NotificationEnvelope.toHistoricalDataEntities() =
        payload
            .map { it as ComponentModel }
            .map {
                HistoricalData(
                    domainComponentName = domainComponentName,
                    id = it.id,
                    organisationName = it.organisationName,
                    businessId = it.businessId ?: EMPTY_UUID,
                    agencyId = EMPTY_UUID,
                    payload = it
                )
            }

    @Suppress("UNCHECKED_CAST")
    private fun delete(event: NotificationEnvelope): EmptyFuture {
        val (mergedDeletes, normalDeletes) = event.payload.partition {
            it is DeleteComponentModel && it.mergedInto != null
        }

        val mergedDeleteIds = generateParamsForMergeDeletes(mergedDeletes, event)

        val normalDeleteIds = generatePramsForNormalDeletes(normalDeletes)

        return retryHandler.retryOnDeadlock {
            db.withTransactionAndReturnEmpty { conn ->
                val mergeFuture = if (mergedDeleteIds.isNotEmpty()) {
                    persistMergeDelete(conn, mergedDeleteIds)
                } else null

                val normalFuture: EmptyFuture? = if (normalDeleteIds.isNotEmpty()) {
                    persistNormalDeletes(normalDeleteIds, event, conn)
                } else null

                listOfNotNull(mergeFuture, normalFuture).join()
            }
        }
    }


    private fun persistMergeDelete(
        conn: PostgresConnection,
        mergedDeleteIds: List<Tuple>
    ) = conn.executePreparedQuery(deleteQueryForMerges, mergedDeleteIds)
        .mapAsEmptyFuture()

    private fun persistNormalDeletes(
        normalDeleteIds: Map<String, List<UUID>>,
        event: NotificationEnvelope,
        conn: PostgresConnection
    ): EmptyFuture {
        val tuples = normalDeleteIds.map({ (orgName, ids) ->
            Tuple.from(listOf(event.domainComponentName, orgName, ids.toTypedArray()))
        })
        return conn.executePreparedQuery(deleteQueryWithoutMerge, tuples)
            .mapAsEmptyFuture()
    }

    private fun generatePramsForNormalDeletes(normalDeletes: List<Identifiable>) = normalDeletes.filterIsInstance<ComponentModel>()
        .filterNot { it is DeleteComponentModel }
        // NB: In theory we should only get one org here due to how it is partitioned higher, up - but do this for safety
        .groupBy({ it.organisationName }, { it.id })

    private fun generateParamsForMergeDeletes(
        mergedDeletes: List<Identifiable>,
        event: NotificationEnvelope
    ) = mergedDeletes.filterIsInstance<DeleteComponentModel>()
        .map {
            listOf(
                event.domainComponentName,
                it.organisationName,
                it.id,
                it.mergedInto != null,
                it.mergedInto?.let { objectMapper.writeValueAsString(JsonSerialisedObjectWrapper(it)) }
            )
        }
        .map { Tuple.from(it) }

    /**
     * Fetches [HistoricalData] from the centralised database. It employs
     * the [SyncAllHistoricalData] as filters for the query.
     */
    fun fetch(event: SyncAllHistoricalData): NotNullFuture<SyncHistoricalDataOutcome> =
        fetchDataFromDatabase(event)
            .map {
                val offset = it.lastOrNull()?.rowNumber ?: Long.MAX_VALUE
                SyncHistoricalDataOutcome(it, offset)
            }

    private fun fetchDataFromDatabase(event: SyncAllHistoricalData) =
        db.withTransaction { conn ->
            val params = event.toQueryParams()
            conn.executePreparedQuery(retrievePageOfDataQuery(event.onlyDeleted), params, HistoricalDataEntity)
        }

    private fun SyncAllHistoricalData.toQueryParams() =
        arrayOf(
            domainComponentName,
            organisationName,
            businessId ?: EMPTY_UUID,
            agency ?: EMPTY_UUID,
            offset,
            since
        )
            .let { Tuple.from(it) }
            .let { listOf(it) }

    /**
     * Fetches a single entry from the database (if it exists).
     */
    fun fetch(event: SyncHistoricalDataById): NullableFuture<HistoricalData> =
        db.withTransactionAndReturnNull { conn ->
            val params = event.toQueryParams()
            conn.executePreparedQuery(retrieveSingleEntryByIdQuery, params, HistoricalDataEntity)
                .composeAsNullable {
                    when (it.size) {
                        0, 1 -> succeededNullableFuture(it.firstOrNull())
                        else -> failedNullableFuture("Query returned more than one entry for id #${event.id}")
                    }
                }
        }

    private fun SyncHistoricalDataById.toQueryParams() =
        arrayOf(
            domainComponentName,
            organisationName,
            businessId ?: EMPTY_UUID,
            agency ?: EMPTY_UUID,
            id
        )
            .let { Tuple.from(it) }
            .let { listOf(it) }

    /**
     * Fetches all data from the historical_data table.
     * Please do not use this in prod.
     */
    @VisibleForTesting
    fun fetchAllEntriesForUnitTest(): NotNullFuture<List<HistoricalData>> =
        db.withTransaction { conn ->
            conn.executeQuery("SELECT * FROM data_api_v2.historical_data WHERE component_name = 'unit_test'", HistoricalDataEntity)
        }

    // if we end up copying this function - it should be moved to the library
    private fun List<HistoricalData>.toFlatTuple(): Tuple =
        this.flatMap { HistoricalDataEntity.extractValuesFrom(it) }
            .let { Tuple.from(it) }

    companion object {
        private const val NUM_ROW_PLACEHOLDERS = 9

        @VisibleForTesting
        const val MAX_RETRIES = 10
    }
}

data class SyncHistoricalDataOutcome(
    val data: List<HistoricalData>,
    val currentOffset: Long,
)
