package nct.data.migrations.arc8278

import injector.Singleton
import nct.data.migrations.partitioningtraverser.PartitioningMigrationSubTaskDef
import nct.data.migrations.partitioningtraverser.PartitioningMigrationTask
import nct.engineering.modern.commons.future.AptEmptyFuture
import nct.engineering.modern.commons.future.NctFutures
import nct.engineering.modern.pg.PostgresClient
import org.slf4j.LoggerFactory

const val ARC8278_MIGRATION_TASK_NAME = "arc8278_marketlisting_clean"

@Singleton
class MarketListingInvalidRecordsRemover(
    private val db: PostgresClient,
) : PartitioningMigrationTask {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(subtask: PartitioningMigrationSubTaskDef): AptEmptyFuture {

        return db.withTransactionAndReturnEmpty { conn ->
            val deleteQuery = """
                DELETE FROM data_api_v2.historical_data
                WHERE org_name = '${subtask.organisationName}'
                  AND component_name = '${subtask.domainComponentName}'
                  AND id >= '${subtask.keyRange.start}'::uuid
                  AND id <= '${subtask.keyRange.end}'::uuid
                  AND (payload::text NOT ILIKE '%lastTouched%' or payload::text NOT ILIKE '%suburbId%')
            """.trimIndent()

            conn.executeQuery(deleteQuery)
                .compose { result ->
                    val deletedCount = result.rowCount()
                    logger.info("Completed removal of invalid market listing records. Deleted $deletedCount records")
                    NctFutures.succeededFuture(deletedCount)
                }.mapAsEmptyFuture()
        }
    }
}
