package nct.data.migrations.arc8278

import kos.api.KosContext
import kos.rest.GET
import kos.rest.RestApi
import nct.data.migrations.partitioningtraverser.MigrationCoordinator
import nct.data.migrations.partitioningtraverser.PartitioningMigrationTaskDef
import nct.engineering.modern.commons.future.AptEmptyFuture
import nct.engineering.modern.commons.future.NctFutures.join

@RestApi("/management/admin/migrations/arc8278/")
class MigrationArc8278Api(
    private val migrationCoordinator: MigrationCoordinator,
    private val kosContext: KosContext,
) {
    @GET("trigger")
    fun removeInvalidRecords(): AptEmptyFuture =
        listOf(
            "raywhitedevelopment", "raywhitedevelopmentnz",
            "raywhitetesting", "raywhitetestingnz",
            "raywhitedevelopmentdemo", "raywhitedevelopmentnzdemo",
            "raywhitedemo", "raywhitenzdemo",
            "raywhiteproddemo", "raywhitenzproddemo"
        ).map { orgName ->
            migrationCoordinator.startMigrationTask(
                PartitioningMigrationTaskDef(
                    organisationName = orgName,
                    domainComponentName = "marketlisting",
                    taskName = ARC8278_MIGRATION_TASK_NAME,
                    partitionWeight = 12
                )
            )
        }.join()
}
