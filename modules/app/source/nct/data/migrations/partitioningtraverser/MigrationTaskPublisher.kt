package nct.data.migrations.partitioningtraverser

import kos.events.Publisher
import nct.engineering.modern.commons.future.AptEmptyFuture

interface MigrationTaskPublisher {
    @Publisher("gcp::pubsub::\${TOPIC_TASK_MIGRATION}")
    fun publish(event: PartitioningMigrationTaskDef): AptEmptyFuture

    @Publisher("gcp::pubsub::\${TOPIC_TASK_MIGRATION_BATCH}")
    fun publish(event: PartitioningMigrationSubTaskDef): AptEmptyFuture
}
