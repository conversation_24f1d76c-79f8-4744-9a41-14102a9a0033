package nct.data.migrations.partitioningtraverser

import java.util.*

/**
 * `KeyParser` is a utility class for generating key ranges based on a partition weight.
 * It divides the UUID space into multiple ranges, which can be used for partitioning data during migrations.
 * The actual partition number is 2^partition_weight
 */
object KeyParser {

    fun parseKeyRange(partitionWeight: Int): List<KeyRange> {
        require(partitionWeight < 64) { "Partition weight must be less than 64" }
        require(partitionWeight >= 0) { "Partition weight must be non-negative" }

        // Base case optimization - avoid calculations for simple cases and demonstrate how UUID works using most/least significant bits
        if (partitionWeight <= 1) {
            return listOf(
                // Positive Long range: 00000000-0000-0000 to 7FFFFFFF-FFFF-FFFF
                KeyRange(
                    start = UUID(0, 0).toString(),
                    end = UUID(Long.MIN_VALUE, 0).toString()
                ),
                // Negative Long range: *************-0000 to FFFFFFFF-FFFF-FFFF
                KeyRange(
                    start = UUID(Long.MIN_VALUE, 0).toString(),
                    end = UUID(-1, -1).toString()
                )
            )
        }

        val range = 1L shl (64 - partitionWeight) // 2^(64-partitionWeight)
        val halfPartitions = 1L shl (partitionWeight - 1) // 2^partitionWeight / 2

        // Pre-allocate list with known size for better performance
        val totalPartitions = 1L shl partitionWeight
        val result = ArrayList<KeyRange>(totalPartitions.toInt())

        // Process positive values (0 to Long.MAX_VALUE)
        processPositiveRange(result, range, halfPartitions)

        // Process negative values (Long.MIN_VALUE to -1)
        processNegativeRange(result, range, halfPartitions)

        return result
    }

    private fun processPositiveRange(result: MutableList<KeyRange>, range: Long, halfPartitions: Long) {
        // Process all but the last positive partition
        for (i in 0 until halfPartitions - 1) {
            val start = i * range
            val end = start + range
            result.add(
                KeyRange(
                    start = UUID(start, 0).toString(),
                    end = UUID(end, 0).toString()
                )
            )
        }

        // Add the boundary partition from last positive to Long.MIN_VALUE
        result.add(
            KeyRange(
                start = UUID((halfPartitions - 1) * range, 0).toString(),
                end = UUID(Long.MIN_VALUE, 0).toString()
            )
        )
    }

    private fun processNegativeRange(result: MutableList<KeyRange>, range: Long, halfPartitions: Long) {
        // Process all but the last negative partition
        for (i in 0 until halfPartitions - 1) {
            val start = Long.MIN_VALUE + i * range
            val end = start + range
            result.add(
                KeyRange(
                    start = UUID(start, 0).toString(),
                    end = UUID(end, 0).toString()
                )
            )
        }

        // Add the final partition to the end boundary
        result.add(
            KeyRange(
                start = UUID(Long.MIN_VALUE + (halfPartitions - 1) * range, 0).toString(),
                end = UUID(-1, -1).toString()
            )
        )
    }
}


data class KeyRange(
    val start: String,
    val end: String,
)
