package nct.data.migrations.partitioningtraverser

import nct.engineering.modern.commons.future.AptEmptyFuture

data class PartitioningMigrationTaskDef (
    val organisationName: String,
    val domainComponentName: String,
    val taskName: String,
    val partitionWeight: Int // the UUID range will be divided into 16^`partitionWeight` parts
)

data class PartitioningMigrationSubTaskDef(
    val organisationName: String,
    val domainComponentName: String,
    val taskName: String,
    val keyRange: KeyRange
)

interface PartitioningMigrationTask {
    fun execute(subtask: PartitioningMigrationSubTaskDef): AptEmptyFuture
}


