package nct.data.migrations.partitioningtraverser

import injector.Singleton
import nct.data.migrations.arc8278.ARC8278_MIGRATION_TASK_NAME
import nct.data.migrations.arc8278.MarketListingInvalidRecordsRemover
import nct.engineering.modern.commons.future.AptEmptyFuture
import nct.engineering.modern.commons.future.NctFutures.join

/*
    The migration coordinator assumes all migration job consists of two tasks:
    - The root task that kicks off the migration job.
    - The subtasks that actually performs the change.
 */
@Singleton
class MigrationCoordinator(
    private val migrationTaskPublisher: MigrationTaskPublisher,
    private val marketListingInvalidRecordsRemover: MarketListingInvalidRecordsRemover
) {
    fun startMigrationTask(taskDef: PartitioningMigrationTaskDef): AptEmptyFuture {
        return migrationTaskPublisher.publish(taskDef)
    }

    fun runMigrationTask(rootTask: PartitioningMigrationTaskDef): AptEmptyFuture {

       val keyRanges = KeyParser.parseKeyRange(rootTask.partitionWeight)

        return keyRanges.map { keyRange ->
            PartitioningMigrationSubTaskDef(
                organisationName = rootTask.organisationName,
                domainComponentName = rootTask.domainComponentName,
                taskName = rootTask.taskName,
                keyRange = keyRange,
            )
        }.map {
            migrationTaskPublisher.publish(it)
        }.join()
    }

    fun runMigrationSubTask(subTaskDef: PartitioningMigrationSubTaskDef): AptEmptyFuture {
        val taskExecutor = getTaskExecutor(subTaskDef.taskName)
        return taskExecutor.execute(subTaskDef)
    }

    private fun getTaskExecutor(taskName: String): PartitioningMigrationTask {
        return when (taskName) {
            ARC8278_MIGRATION_TASK_NAME -> marketListingInvalidRecordsRemover
            else -> throw IllegalArgumentException("Unknown migration task: $taskName")
        }
    }
}
