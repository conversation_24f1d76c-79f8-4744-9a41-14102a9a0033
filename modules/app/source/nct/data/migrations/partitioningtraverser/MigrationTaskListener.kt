package nct.data.migrations.partitioningtraverser

import injector.Singleton
import kos.events.Listener
import nct.engineering.modern.commons.future.AptEmptyFuture

@Singleton
class MigrationTaskListener(
    private val migrationCoordinator: MigrationCoordinator
) {
    @Listener("gcp::pubsub::\${TOPIC_TASK_MIGRATION}?max_in_flight=5")
    fun on(event: PartitioningMigrationTaskDef): AptEmptyFuture =
        migrationCoordinator.runMigrationTask(event)

    @Listener("gcp::pubsub::\${TOPIC_TASK_MIGRATION_BATCH}?max_in_flight=5")
    fun on(event: PartitioningMigrationSubTaskDef): AptEmptyFuture =
        migrationCoordinator.runMigrationSubTask(event)

}
