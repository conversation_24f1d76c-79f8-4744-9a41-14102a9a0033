package nct.data.coordination.synchronisation.adapters.sql

import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import nct.data.coordination.synchronisation.models.HistoricalData
import nct.data.schemas.commons.ComponentModel
import nct.data.schemas.commons.CorrelationId
import nct.data.schemas.commons.DefaultWebhookNotificationEnvelope
import nct.data.schemas.commons.DeleteComponentModel
import nct.data.schemas.commons.IdOnlyModel
import nct.data.schemas.commons.PayloadState
import nct.data.schemas.commons.WellKnownWebhookPubSubAttributes.state
import nct.engineering.modern.commons.KosTestSupport.Companion.awaitForResult
import nct.engineering.modern.commons.future.EmptyFuture
import nct.engineering.modern.commons.future.NctFutures
import nct.engineering.modern.commons.future.exceptionWrapper
import nct.engineering.modern.pg.PostgresClient
import nct.engineering.modern.pg.PostgresConnection
import net.bytebuddy.description.type.TypeDefinition.Sort.describe
import org.slf4j.Logger
import java.sql.SQLException
import java.util.*

class HistoricalDataRepositoryDeadlockRetryTest : DescribeSpec({

    val mockDb = mockk<PostgresClient>()
    val mockObjectMapper = mockk<ObjectMapper>()
    val repository = HistoricalDataRepository(mockDb, mockObjectMapper)


    describe("isDeadlockException") {

        it("should return true for SQLException with SQL state 40001") {
            val exception = SQLException("Serialization failure", "40001")
            val result = repository.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for SQLException with SQL state 40P01") {
            val exception = SQLException("Deadlock detected", "40P01")
            val result = repository.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for exception message containing '40001'") {
            val exception = RuntimeException("Database error: 40001 - serialization failure")
            val result = repository.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for exception message containing '40p01'") {
            val exception = RuntimeException("Database error: 40p01 - deadlock detected")
            val result = repository.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for exception message containing 'deadlock'") {
            val exception = RuntimeException("Transaction failed due to deadlock")
            val result = repository.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for exception message containing 'serialization failure'") {
            val exception = RuntimeException("Could not complete due to serialization failure")
            val result = repository.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for exception message containing 'could not serialize access'") {
            val exception = RuntimeException("could not serialize access due to concurrent update")
            val result = repository.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for nested exception with deadlock") {
            val rootCause = SQLException("Deadlock detected", "40P01")
            val exception = RuntimeException("Transaction failed", rootCause)
            val result = repository.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return false for non-deadlock exceptions") {
            val exception = RuntimeException("Some other database error")
            val result = repository.isDeadlockException(exception)
            result shouldBe false
        }

        it("should return false for SQLException with different SQL state") {
            val exception = SQLException("Constraint violation", "23505")
            val result = repository.isDeadlockException(exception)
            result shouldBe false
        }
    }

    describe("retryOnDeadlock") {

        it("should succeed on first attempt when operation succeeds") {
            var callCount = 0
            val operation = {
                callCount++
                NctFutures.succeededEmptyFuture()
            }

            repository.retryOnDeadlock(operation)

            callCount shouldBe 1
        }

        it("should retry on deadlock exception and eventually succeed") {
            var callCount = 0
            val operation = {
                callCount++
                if (callCount < 3) {
                    exceptionWrapper().wrapAsEmptyFuture(SQLException("Deadlock detected", "40P01"))
                } else {
                    NctFutures.succeededEmptyFuture()
                }
            }

            repository.retryOnDeadlock(operation).awaitForResult()

            callCount shouldBe 3
        }

        it("should throw exception after max retries exceeded") {
            val exception = SQLException("Deadlock detected", "40P01")
            var callCount = 0
            val operation = {
                callCount++
                exceptionWrapper().wrapAsEmptyFuture(exception)
            }


            val result = shouldThrow<Throwable> { repository.retryOnDeadlock(operation).awaitForResult() }

            result.cause shouldBe exception
            callCount shouldBe HistoricalDataRepository.MAX_RETRIES + 1
        }

        it("should not retry on non-deadlock exceptions") {
            val exception = RuntimeException("Some other error")
            var callCount = 0
            val operation = {
                callCount++
                throw exception
            }


            val result = shouldThrow<Throwable> { repository.retryOnDeadlock(operation) }

            result shouldBe exception
            callCount shouldBe 1
        }
    }

    describe("upsert with retry logic") {

        it("should retry upsert operation on deadlock") {
            val event = createTestEvent()

            var attemptCount = 0
            every { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) } answers {
                attemptCount++
                if (attemptCount <= 2) {
                    NctFutures.failedEmptyFuture("Deadlock detected", SQLException("Deadlock detected", "40P01"))
                } else {
                    NctFutures.succeededEmptyFuture()
                }
            }

            val result = repository.ingest(event)

            verify(exactly = 3) { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) }
        }
    }

    describe("delete with retry logic") {

        it("should retry delete operation on deadlock for normal deletes") {
            val event = createTestDeleteEventWithoutMerge()

            var attemptCount = 0
            every { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) } answers {
                attemptCount++
                if (attemptCount == 1) {
                    NctFutures.failedEmptyFuture("Serialization failure", SQLException("Serialization failure", "40001"))
                } else {
                    NctFutures.succeededEmptyFuture()
                }
            }

            val result = repository.ingest(event)

            // The retry logic should call the database operation twice (first fails, second succeeds)
            verify(atLeast = 2) { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) }
        }

        it("should retry delete operation on deadlock for merge deletes") {
            val event = createTestDeleteEvent()

            // Mock the ObjectMapper to return a JSON string
            every { mockObjectMapper.writeValueAsString(any()) } returns """{"id":"test-id","organisationName":"test_org","businessId":"test-business-id"}"""

            var attemptCount = 0
            every { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) } answers {
                attemptCount++
                if (attemptCount == 1) {
                    NctFutures.failedEmptyFuture("Deadlock detected", SQLException("Deadlock detected", "40P01"))
                } else {
                    NctFutures.succeededEmptyFuture()
                }
            }

            val result = repository.ingest(event)

            // The retry logic should call the database operation twice (first fails, second succeeds)
            verify(atLeast = 2) { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) }
        }
    }
})

private fun createTestEvent(): DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope {
    return DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope(
        domainComponentName = "test_component",
        state = PayloadState.Created,
        payload = listOf(createTestComponentModel())
    )
}

private fun createTestDeleteEvent(): DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope {
    return DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope(
        domainComponentName = "test_component",
        state = PayloadState.Deleted,
        payload = listOf(createTestDeleteComponentModel())
    )
}

private fun createTestDeleteEventWithoutMerge(): DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope {
    return DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope(
        domainComponentName = "test_component",
        state = PayloadState.Deleted,
        payload = listOf(createTestDeleteComponentModelWithoutMerge())
    )
}

private fun createTestComponentModel(): ComponentModel {
    return object : ComponentModel {
        override val id = UUID.randomUUID()
        override val organisationName = "test_org"
        override val businessId = UUID.randomUUID()
        override val correlationId = CorrelationId.NurtureCloud
    }
}

private fun createTestDeleteComponentModel(): DeleteComponentModel {
    return DeleteComponentModel(
        id = UUID.randomUUID(),
        organisationName = "test_org",
        businessId = UUID.randomUUID(),
        mergedInto = IdOnlyModel(
            id = UUID.randomUUID(),
            organisationName = "test_org",
            businessId = UUID.randomUUID()
        )
    )
}

private fun createTestDeleteComponentModelWithoutMerge(): DeleteComponentModel {
    return DeleteComponentModel(
        id = UUID.randomUUID(),
        organisationName = "test_org",
        businessId = UUID.randomUUID(),
        mergedInto = null
    )
}
