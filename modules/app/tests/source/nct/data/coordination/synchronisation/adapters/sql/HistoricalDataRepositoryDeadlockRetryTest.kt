package nct.data.coordination.synchronisation.adapters.sql

import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContain
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import nct.data.coordination.synchronisation.models.HistoricalData
import nct.data.schemas.commons.ComponentModel
import nct.data.schemas.commons.CorrelationId
import nct.data.schemas.commons.DefaultWebhookNotificationEnvelope
import nct.data.schemas.commons.DeleteComponentModel
import nct.data.schemas.commons.IdOnlyModel
import nct.data.schemas.commons.PayloadState
import nct.data.schemas.commons.WellKnownWebhookPubSubAttributes.state
import nct.engineering.modern.commons.KosTestSupport.Companion.awaitForResult
import nct.engineering.modern.commons.future.EmptyFuture
import nct.engineering.modern.commons.future.NctFutures
import nct.engineering.modern.commons.future.exceptionWrapper
import nct.engineering.modern.pg.PostgresClient
import nct.engineering.modern.pg.PostgresConnection
import nct.engineering.vertx.sqlclient.DatabaseRetryHandler
import net.bytebuddy.description.type.TypeDefinition.Sort.describe
import org.slf4j.Logger
import java.sql.SQLException
import java.util.*

class HistoricalDataRepositoryDeadlockRetryTest : DescribeSpec({

    val mockDb = mockk<PostgresClient>()
    val mockObjectMapper = mockk<ObjectMapper>()
    val retryHandler = DatabaseRetryHandler()
    val repository = HistoricalDataRepository(mockDb, mockObjectMapper, retryHandler)

    describe("upsert with retry logic") {

        it("should retry upsert operation on deadlock") {
            val event = createTestEvent()

            var attemptCount = 0
            every { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) } answers {
                attemptCount++
                if (attemptCount <= 2) {
                    NctFutures.failedEmptyFuture("Deadlock detected", SQLException("Deadlock detected", "40P01"))
                } else {
                    NctFutures.succeededEmptyFuture()
                }
            }

            val result = repository.ingest(event)

            verify(exactly = 3) { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) }
        }
    }

    describe("delete with retry logic") {

        it("should retry delete operation on deadlock for normal deletes") {
            val event = createTestDeleteEventWithoutMerge()

            var attemptCount = 0
            every { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) } answers {
                attemptCount++
                if (attemptCount == 1) {
                    NctFutures.failedEmptyFuture("Serialization failure", SQLException("Serialization failure", "40001"))
                } else {
                    NctFutures.succeededEmptyFuture()
                }
            }

            val result = repository.ingest(event)

            // The retry logic should call the database operation twice (first fails, second succeeds)
            verify(atLeast = 2) { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) }
        }

        it("should retry delete operation on deadlock for merge deletes") {
            val event = createTestDeleteEvent()

            // Mock the ObjectMapper to return a JSON string
            every { mockObjectMapper.writeValueAsString(any()) } returns """{"id":"test-id","organisationName":"test_org","businessId":"test-business-id"}"""

            var attemptCount = 0
            every { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) } answers {
                attemptCount++
                if (attemptCount == 1) {
                    NctFutures.failedEmptyFuture("Deadlock detected", SQLException("Deadlock detected", "40P01"))
                } else {
                    NctFutures.succeededEmptyFuture()
                }
            }

            val result = repository.ingest(event)

            // The retry logic should call the database operation twice (first fails, second succeeds)
            verify(atLeast = 2) { mockDb.withTransactionAndReturnEmpty(any<(PostgresConnection) -> EmptyFuture>()) }
        }
    }
})

private fun createTestEvent(): DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope {
    return DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope(
        domainComponentName = "test_component",
        state = PayloadState.Created,
        payload = listOf(createTestComponentModel())
    )
}

private fun createTestDeleteEvent(): DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope {
    return DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope(
        domainComponentName = "test_component",
        state = PayloadState.Deleted,
        payload = listOf(createTestDeleteComponentModel())
    )
}

private fun createTestDeleteEventWithoutMerge(): DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope {
    return DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope(
        domainComponentName = "test_component",
        state = PayloadState.Deleted,
        payload = listOf(createTestDeleteComponentModelWithoutMerge())
    )
}

private fun createTestComponentModel(): ComponentModel {
    return object : ComponentModel {
        override val id = UUID.randomUUID()
        override val organisationName = "test_org"
        override val businessId = UUID.randomUUID()
        override val correlationId = CorrelationId.NurtureCloud
    }
}

private fun createTestDeleteComponentModel(): DeleteComponentModel {
    return DeleteComponentModel(
        id = UUID.randomUUID(),
        organisationName = "test_org",
        businessId = UUID.randomUUID(),
        mergedInto = IdOnlyModel(
            id = UUID.randomUUID(),
            organisationName = "test_org",
            businessId = UUID.randomUUID()
        )
    )
}

private fun createTestDeleteComponentModelWithoutMerge(): DeleteComponentModel {
    return DeleteComponentModel(
        id = UUID.randomUUID(),
        organisationName = "test_org",
        businessId = UUID.randomUUID(),
        mergedInto = null
    )
}
