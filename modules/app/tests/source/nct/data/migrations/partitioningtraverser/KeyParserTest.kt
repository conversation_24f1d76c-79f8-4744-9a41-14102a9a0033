package nct.data.migrations.partitioningtraverser

import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe

class KeyParserTest : DescribeSpec({

    val expected2PartitionList = listOf(
        KeyRange("00000000-0000-0000-0000-000000000000", "80000000-0000-0000-0000-000000000000"),
        <PERSON><PERSON><PERSON><PERSON>("80000000-0000-0000-0000-000000000000", "ffffffff-ffff-ffff-ffff-ffffffffffff")
    )

    val expected4PartitionList = listOf(
        KeyRange("00000000-0000-0000-0000-000000000000", "40000000-0000-0000-0000-000000000000"),
        <PERSON>R<PERSON><PERSON>("40000000-0000-0000-0000-000000000000", "80000000-0000-0000-0000-000000000000"),
        <PERSON><PERSON><PERSON><PERSON>("80000000-0000-0000-0000-000000000000", "c0000000-0000-0000-0000-000000000000"),
        <PERSON><PERSON><PERSON><PERSON>("c0000000-0000-0000-0000-000000000000", "ffffffff-ffff-ffff-ffff-ffffffffffff")
    )

    val expected8PartitionList = listOf(
        KeyRange("00000000-0000-0000-0000-000000000000", "20000000-0000-0000-0000-000000000000"),
        KeyRange("20000000-0000-0000-0000-000000000000", "40000000-0000-0000-0000-000000000000"),
        KeyRange("40000000-0000-0000-0000-000000000000", "60000000-0000-0000-0000-000000000000"),
        KeyRange("60000000-0000-0000-0000-000000000000", "80000000-0000-0000-0000-000000000000"),
        KeyRange("80000000-0000-0000-0000-000000000000", "a0000000-0000-0000-0000-000000000000"),
        KeyRange("a0000000-0000-0000-0000-000000000000", "c0000000-0000-0000-0000-000000000000"),
        KeyRange("c0000000-0000-0000-0000-000000000000", "e0000000-0000-0000-0000-000000000000"),
        KeyRange("e0000000-0000-0000-0000-000000000000", "ffffffff-ffff-ffff-ffff-ffffffffffff")
    )


    describe("When trying to partition with weight of 1 ") {
        it("should return 2 partitions") {
            val result = KeyParser.parseKeyRange(1)
            result shouldBe expected2PartitionList
        }
    }

    describe("When trying to partition with weight of 2 ") {
        it("should return 4 partitions") {
            val result = KeyParser.parseKeyRange(2)
            result shouldBe  expected4PartitionList
        }
    }

    describe("When trying to partition with weight of 3 ") {
        it("should return 8 partitions") {
            val result = KeyParser.parseKeyRange(3)
            result shouldBe expected8PartitionList
        }
    }
})
