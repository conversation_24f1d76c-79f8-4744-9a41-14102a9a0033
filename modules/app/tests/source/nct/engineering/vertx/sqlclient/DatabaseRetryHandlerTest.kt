package nct.engineering.vertx.sqlclient

import io.kotest.assertions.throwables.shouldThrow
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import nct.engineering.modern.commons.KosTestSupport.Companion.awaitForResult
import nct.engineering.modern.commons.future.NctFutures
import nct.engineering.modern.commons.future.exceptionWrapper
import java.sql.SQLException

class DatabaseRetryHandlerTest : DescribeSpec({

    val retryHandler = DatabaseRetryHandler()

    describe("isDeadlockException") {

        it("should return true for exception message containing '40001'") {
            val exception = RuntimeException("Database error: 40001 - serialization failure")
            val result = retryHandler.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for exception message containing '40p01'") {
            val exception = RuntimeException("Database error: 40p01 - deadlock detected")
            val result = retryHandler.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for exception message containing 'deadlock'") {
            val exception = RuntimeException("Transaction failed due to deadlock")
            val result = retryHandler.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for exception message containing 'serialization failure'") {
            val exception = RuntimeException("Could not complete due to serialization failure")
            val result = retryHandler.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for exception message containing 'could not serialize access'") {
            val exception = RuntimeException("could not serialize access due to concurrent update")
            val result = retryHandler.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for nested exception with deadlock") {
            val rootCause = SQLException("Deadlock detected", "40P01")
            val exception = RuntimeException("Transaction failed", rootCause)
            val result = retryHandler.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for SQLException with SQL state 40001") {
            val exception = SQLException("Serialization failure", "40001")
            val result = retryHandler.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return true for SQLException with SQL state 40P01") {
            val exception = SQLException("Deadlock detected", "40P01")
            val result = retryHandler.isDeadlockException(exception)
            result shouldBe true
        }

        it("should return false for non-deadlock exceptions") {
            val exception = RuntimeException("Some other database error")
            val result = retryHandler.isDeadlockException(exception)
            result shouldBe false
        }
    }

    describe("retryOnDeadlock") {

        it("should succeed on first attempt when operation succeeds") {
            var callCount = 0
            val operation = {
                callCount++
                NctFutures.succeededEmptyFuture()
            }

            retryHandler.retryOnDeadlock(operation).awaitForResult()

            callCount shouldBe 1
        }

        it("should retry on deadlock exception and eventually succeed") {
            var callCount = 0
            val operation = {
                callCount++
                if (callCount < 3) {
                    exceptionWrapper().wrapAsEmptyFuture(SQLException("Deadlock detected", "40P01"))
                } else {
                    NctFutures.succeededEmptyFuture()
                }
            }

            retryHandler.retryOnDeadlock(operation).awaitForResult()

            callCount shouldBe 3
        }

        it("should throw exception after max retries exceeded") {
            val exception = SQLException("Deadlock detected", "40P01")
            var callCount = 0
            val operation = {
                callCount++
                exceptionWrapper().wrapAsEmptyFuture(exception)
            }

            val result = shouldThrow<Throwable> {
                retryHandler.retryOnDeadlock(operation).awaitForResult()
            }

            result.cause shouldBe exception
            callCount shouldBe DatabaseRetryHandler.DEFAULT_MAX_RETRIES + 1
        }

        it("should not retry on non-deadlock exceptions") {
            val exception = RuntimeException("Some other error")
            var callCount = 0
            val operation = {
                callCount++
                throw exception
            }

            val result = shouldThrow<Throwable> {
                retryHandler.retryOnDeadlock(operation).awaitForResult()
            }

            result shouldBe exception
            callCount shouldBe 1
        }

        it("should retry on serialization failure") {
            var callCount = 0
            val operation = {
                callCount++
                if (callCount == 1) {
                    exceptionWrapper().wrapAsEmptyFuture(SQLException("Serialization failure", "40001"))
                } else {
                    NctFutures.succeededEmptyFuture()
                }
            }

            retryHandler.retryOnDeadlock(operation).awaitForResult()

            callCount shouldBe 2
        }
    }
})
