package nct.data.coordination.synchronisation.adapters.sql

import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainAll
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.comparables.shouldBeGreaterThan
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import nct.data.coordination.synchronisation.adapters.sql.HistoricalDataRepositoryTestScope.DEFAULT_BUSINESS_ID
import nct.data.coordination.synchronisation.adapters.sql.HistoricalDataRepositoryTestScope.createEntitySamples
import nct.data.coordination.synchronisation.adapters.sql.HistoricalDataRepositoryTestScope.createEnvelope
import nct.data.coordination.synchronisation.adapters.sql.HistoricalDataRepositoryTestScope.createQueryToSyncById
import nct.data.coordination.synchronisation.adapters.sql.HistoricalDataRepositoryTestScope.createQueryToSyncData
import nct.data.coordination.synchronisation.adapters.sql.HistoricalDataRepositoryTestScope.interleaveMergeWith
import nct.data.coordination.synchronisation.models.SyncAllHistoricalData
import nct.data.coordination.synchronisation.models.SyncHistoricalDataById
import nct.data.schemas.commons.ComponentModel
import nct.data.schemas.commons.CorrelationId
import nct.data.schemas.commons.DefaultWebhookNotificationEnvelope
import nct.data.schemas.commons.DeleteComponentModel
import nct.data.schemas.commons.IdOnlyModel
import nct.data.schemas.commons.PayloadState
import nct.data.security.models.AccountId
import nct.engineering.modern.commons.KosTestSupport
import nct.engineering.modern.commons.KosTestSupport.Companion.awaitForResult
import nct.engineering.modern.pg.PostgresClient
import java.time.OffsetDateTime
import java.time.temporal.ChronoUnit
import java.util.*

class HistoricalDataRepositoryTest : DescribeSpec({
    val kosTestSupport = KosTestSupport()
    val db = kosTestSupport.fetchInstance<PostgresClient>()
    val objectMapper = kosTestSupport.fetchInstance<ObjectMapper>()
    val repository = HistoricalDataRepository(db, objectMapper)

    afterEach {
        db.withConnection { conn ->
            conn.executeQuery("DELETE FROM data_api_v2.historical_data").compose {
                conn.executeQuery("ALTER SEQUENCE data_api_v2.historical_data_row_numbers RESTART WITH 0")
            }
        }.awaitForResult()
    }

    afterSpec { db.close().awaitForResult() }

    describe("when persisting no entities") {

        val event = createEnvelope(PayloadState.Created, emptyList())

        it("should not store anything into the database") {
            repository.ingest(event).awaitForResult()

            val result = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
            result.size shouldBe 0
        }
    }

    describe("when persisting a single entity") {
        val sampleEntities = createEntitySamples(1, UUID.randomUUID())
        val event = createEnvelope(PayloadState.Created, sampleEntities)

        it("should persist with the correct data") {
            repository.ingest(event).awaitForResult()

            val entities = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
            entities.map { it.payload } shouldBe sampleEntities
        }

        it("should have the correct row_number") {
            repository.ingest(event).awaitForResult()

            val row = repository.fetchAllEntriesForUnitTest().awaitForResult()!!.first()
            row.rowNumber shouldBe 0
        }

        it("should have the correct last_touched") {
            repository.ingest(event).awaitForResult()

            val row = repository.fetchAllEntriesForUnitTest().awaitForResult()!!.first()
            row.lastTouched shouldBeGreaterThan OffsetDateTime.now().minusHours(1)
        }
    }

    describe("when persisting multiple entities in the same business") {
        val sampleEntities = createEntitySamples(2, DEFAULT_BUSINESS_ID)
        val event = createEnvelope(PayloadState.Created, sampleEntities)

        it("should persist with the correct data") {
            repository.ingest(event).awaitForResult()

            val entities = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
            entities.size shouldBe 2
            entities.map { it.payload } shouldContainExactlyInAnyOrder sampleEntities
        }

        it("should have the correct row_number") {
            repository.ingest(event).awaitForResult()

            val rows = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
            val rowNumbers = rows.map { it.rowNumber }
            rowNumbers shouldContainExactlyInAnyOrder listOf(0, 1)
        }

        it("should have the correct last_touched") {
            repository.ingest(event).awaitForResult()

            val rows = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
            val lastTouches = rows.map { it.lastTouched }
            lastTouches.forEach { it shouldBeGreaterThan OffsetDateTime.now().minusSeconds(1) }
        }
    }

    describe("when persisting multiple entities in different businesses") {
        val sampleEntity1 = createEntitySamples(1, UUID.randomUUID())
        val sampleEntity2 = createEntitySamples(1, UUID.randomUUID())
        val sampleEntities = sampleEntity1 + sampleEntity2
        val event = createEnvelope(PayloadState.Created, sampleEntities)

        it("should persist with the correct data") {
            repository.ingest(event).awaitForResult()

            val entities = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
            entities.size shouldBe 2
            entities.map { it.payload } shouldContainExactlyInAnyOrder sampleEntities
        }

        it("should have the correct row_number") {
            repository.ingest(event).awaitForResult()

            val rows = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
            val rowNumbers = rows.map { it.rowNumber }
            rowNumbers shouldContainExactlyInAnyOrder listOf(0, 1)
        }

        it("should have the correct last_touched") {
            val timeBeforeCreate = OffsetDateTime.now().minus(100, ChronoUnit.MILLIS)

            repository.ingest(event).awaitForResult()

            val rows = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
            val lastTouches = rows.map { it.lastTouched }
            lastTouches.forEach { it shouldBeGreaterThan timeBeforeCreate }
        }

        describe("when data is updated") {

            val timeBeforeUpdate = OffsetDateTime.now()

            beforeEach {
                repository.ingest(event).awaitForResult()
            }

            it("should not move the row_number") {
                val rows = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
                val rowNumbers = rows.map { it.rowNumber }
                rowNumbers shouldContainExactlyInAnyOrder  listOf(0, 1)
            }

            it("should have the correct last_touched") {
                val rows = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
                val lastTouches = rows.map { it.lastTouched }
                lastTouches.forEach { it shouldBeGreaterThan timeBeforeUpdate }
            }
        }

        describe("when data is deleted") {

            beforeEach {
                repository.ingest(event).awaitForResult()
            }

            describe("when delete is not a merge") {
                it("is should be flagged as deleted") {
                    val deleteEvent = event.copy(state = PayloadState.Deleted)
                    repository.ingest(deleteEvent).awaitForResult()

                    val rows = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
                    rows.forEach { it.isDeleted shouldBe true }
                }
            }

            describe("when delete is a merge") {
                it("is should be flagged as deleted and store the merge data") {
                    val mergedIntoId = UUID.randomUUID()
                    val mergedIntoBusinessId = UUID.randomUUID()
                    val deleteEvent = event.copy(
                        state = PayloadState.Deleted,
                        payload = event.payload.map {
                            DeleteComponentModel(
                                id = it.id,
                                businessId = it.businessId,
                                organisationName = it.organisationName,
                                mergedInto = IdOnlyModel(
                                    id = mergedIntoId,
                                    businessId = mergedIntoBusinessId,
                                    organisationName = it.organisationName
                                )
                            )
                        }
                    )
                    repository.ingest(deleteEvent).awaitForResult()

                    val rows = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
                    rows.forEach {
                        it.isDeleted shouldBe true
                        it.deleteIsMerge shouldBe true
                        val mergePayload = it.mergePayload.shouldNotBeNull()
                        mergePayload.id shouldBe mergedIntoId
                        mergePayload.businessId shouldBe mergedIntoBusinessId
                        mergePayload.organisationName shouldBe it.organisationName
                    }
                }
            }
        }
    }

    describe("when fetching an entity by id") {
        val wellKnownEntity = createEntitySamples(1, DEFAULT_BUSINESS_ID)
        val otherEntities = createEntitySamples(2, UUID.randomUUID())
        val sampleEntities = wellKnownEntity.interleaveMergeWith(otherEntities)
        val event = createEnvelope(PayloadState.Created, sampleEntities)

        beforeEach {
            repository.ingest(event).awaitForResult()
        }

        describe("when there is no matching entity") {
            it("should return null") {
                val query = createQueryToSyncById(id = UUID.randomUUID())
                val entity = repository.fetch(query).awaitForResult()
                entity shouldBe null
            }
        }

        describe("when there is no matching entity in the business") {
            it("should return null") {
                val query = createQueryToSyncById(businessId = DEFAULT_BUSINESS_ID, id = UUID.randomUUID())
                val entity = repository.fetch(query).awaitForResult()
                entity shouldBe null
            }
        }

        describe("when there is a matching entity") {
            it("should return the correct entity") {
                val query = createQueryToSyncById(businessId = DEFAULT_BUSINESS_ID, id = wellKnownEntity.first().id)
                val entity = repository.fetch(query).awaitForResult()
                entity?.payload shouldBe wellKnownEntity.first()
            }
        }
    }

    describe("when fetching pagination details") {
        val now = OffsetDateTime.now()
        val weekAgo = now.minusDays(7)

        describe("when there are no rows") {
            it("should place the currentOffset at the end (Long.MAX_VALUE)") {
                val params = createQueryToSyncData(businessId = DEFAULT_BUSINESS_ID, since = weekAgo)
                val result = repository.fetch(params).awaitForResult()!!
                result.currentOffset shouldBe Long.MAX_VALUE
            }
            it("should return no data") {
                val params = createQueryToSyncData(businessId = DEFAULT_BUSINESS_ID, since = weekAgo)
                val result = repository.fetch(params).awaitForResult()!!
                result.data.shouldBeEmpty()
            }
        }

        describe("when there is data in the db") {

            val wellKnownEntities = createEntitySamples(300, DEFAULT_BUSINESS_ID)
            val otherEntities = createEntitySamples(1000, UUID.randomUUID())
            val sampleEntities = wellKnownEntities.interleaveMergeWith(otherEntities)
            val event = createEnvelope(PayloadState.Created, sampleEntities)

            beforeEach {
                val start = System.currentTimeMillis()
                repository.ingest(event).awaitForResult()
                val elapsed = System.currentTimeMillis() - start
                println("Inserted ${event.payload.size} entries in ${elapsed}ms")
            }

            describe("and onlyDeleted flag is set to true") {
                val query = createQueryToSyncData(businessId = DEFAULT_BUSINESS_ID, since = OffsetDateTime.MIN, offset = -1, onlyDeleted = true)

                it("should return only the deleted data") {
                    val deletion = event.copy(state = PayloadState.Deleted, payload = wellKnownEntities.take(2))
                    repository.ingest(deletion).awaitForResult()

                    val result = repository.fetch(query).awaitForResult()!!
                    result.data.size shouldBe 2
                }
            }

            describe("and onlyDeleted flag is set to false") {
                val query = createQueryToSyncData(businessId = DEFAULT_BUSINESS_ID, since = OffsetDateTime.MIN, offset = 1, onlyDeleted = false)

                it("should return all data including the deleted one") {
                    val deletion = event.copy(state = PayloadState.Deleted, payload = wellKnownEntities.take(2))
                    repository.ingest(deletion).awaitForResult()

                    val rows = repository.fetchAllEntriesForUnitTest().awaitForResult()!!
                    rows.size shouldBe 1300

                    val result = repository.fetch(query).awaitForResult()!!
                    result.data.size shouldBe 100
                }
            }

            describe("when the 'offset' is further than it currently is") {

                val query = createQueryToSyncData(businessId = DEFAULT_BUSINESS_ID, since = weekAgo, offset = Long.MAX_VALUE)

                it("should place the currentOffset at the end (Long.MAX_VALUE)") {
                    val result = repository.fetch(query).awaitForResult()!!
                    result.currentOffset shouldBe Long.MAX_VALUE
                }

                it("should return no data") {
                    val result = repository.fetch(query).awaitForResult()!!
                    result.data.shouldBeEmpty()
                }
            }

            describe("when the 'since' is a date in the future of the latest ingested data") {

                val nextMonth = weekAgo.plusMonths(1)
                val query = createQueryToSyncData(businessId = DEFAULT_BUSINESS_ID, since = nextMonth, offset = -1)

                it("should place the currentOffset at the end (Long.MAX_VALUE)") {
                    val result = repository.fetch(query).awaitForResult()!!
                    result.currentOffset shouldBe Long.MAX_VALUE
                }

                it("should return no data") {
                    val result = repository.fetch(query).awaitForResult()!!
                    result.data.shouldBeEmpty()
                }
            }

            describe("when query matches data within an existing businessId") {

                describe("given the 'offset' is at the beginning (-1)") {

                    val query = createQueryToSyncData(businessId = DEFAULT_BUSINESS_ID, since = weekAgo, offset = -1)

                    it("should place the currentOffset at the beginning of the next page (200)") {
                        val result = repository.fetch(query).awaitForResult()!!
                        result.currentOffset shouldBe 198
                    }

                    it("should return the first page of data") {
                        val result = repository.fetch(query).awaitForResult()!!
                        result.data.size shouldBe 100
                        result.data.map { it.payload } shouldContainAll wellKnownEntities.subList(0, 100)
                    }

                    it("should not contain data from other businesses") {
                        val result = repository.fetch(query).awaitForResult()!!
                        result.data.any { it.businessId != DEFAULT_BUSINESS_ID } shouldBe false
                    }
                }

                describe("given the 'offset' is at the end of the current page (199)") {

                    val query = createQueryToSyncData(businessId = DEFAULT_BUSINESS_ID, since = weekAgo, offset = 398)

                    it("should place the currentOffset at the end of the current page") {
                        val result = repository.fetch(query).awaitForResult()!!
                        result.currentOffset shouldBe 598
                    }

                    it("should return the second page of data") {
                        val result = repository.fetch(query).awaitForResult()!!
                        result.data.size shouldBe (wellKnownEntities.size - 200)
                        result.data.map { it.payload } shouldContainAll wellKnownEntities.subList(200, wellKnownEntities.size)
                    }

                    it("should not contain data from other businesses") {
                        val result = repository.fetch(query).awaitForResult()!!
                        result.data.any { it.businessId != DEFAULT_BUSINESS_ID } shouldBe false
                    }
                }
            }
        }
    }
})

private object HistoricalDataRepositoryTestScope {

    const val DEFAULT_COMPONENT_NAME = "unit_test"
    const val DEFAULT_ORG_NAME = "nurture_cloud"
    val DEFAULT_AGENCY_ID = UUID.fromString("00000000-0000-0000-0000-000000000000")!!
    val DEFAULT_BUSINESS_ID = UUID.randomUUID()!!

    fun createEnvelope(
        state: PayloadState,
        payload: List<ComponentModel>,
    ) = DefaultWebhookNotificationEnvelope.BroadcastNotificationEnvelope(
        domainComponentName = DEFAULT_COMPONENT_NAME,
        state = state,
        payload = payload
    )

    fun createEntitySamples(numberOfSamples: Int, businessId: UUID = DEFAULT_BUSINESS_ID) =
        (0 until numberOfSamples).map {
            MyCustomEntity(UUID.randomUUID(), DEFAULT_ORG_NAME, businessId)
        }

    fun createQueryToSyncById(id: UUID, businessId: UUID? = null) = SyncHistoricalDataById(
        accountId = AccountId.randomUUID(),
        agency = DEFAULT_AGENCY_ID,
        businessId = businessId ?: DEFAULT_BUSINESS_ID,
        organisationName = DEFAULT_ORG_NAME,
        domainComponentName = DEFAULT_COMPONENT_NAME,
        id = id
    )

    fun createQueryToSyncData(businessId: UUID? = null, offset: Long = -1, since: OffsetDateTime = OffsetDateTime.now(), onlyDeleted: Boolean = false) = SyncAllHistoricalData(
        accountId = AccountId.randomUUID(),
        agency = DEFAULT_AGENCY_ID,
        businessId = businessId ?: DEFAULT_BUSINESS_ID,
        organisationName = DEFAULT_ORG_NAME,
        domainComponentName = DEFAULT_COMPONENT_NAME,
        since = since,
        offset = offset,
        onlyDeleted = onlyDeleted
    )

    fun <T: Any> List<T>.interleaveMergeWith(target: List<T>): List<T> {
        val interleaved = mutableListOf<T>()
        val first = this.iterator()
        val second = target.iterator()
        while (interleaved.size < this.size + target.size){
            if (first.hasNext()) interleaved.add(first.next())
            if (second.hasNext()) interleaved.add(second.next())
        }
        return interleaved
    }
}

private data class MyCustomEntity(
    override val id: UUID,
    override val organisationName: String,
    override val businessId: UUID?
): ComponentModel {

    override val correlationId = CorrelationId.NurtureCloud
}
