package nct.data.migration.arc8278

import com.fasterxml.jackson.databind.ObjectMapper
import io.kotest.core.spec.style.DescribeSpec
import io.kotest.matchers.shouldBe
import nct.data.migrations.arc8278.*
import nct.data.migrations.partitioningtraverser.KeyParser
import nct.data.migrations.partitioningtraverser.PartitioningMigrationSubTaskDef
import nct.engineering.modern.commons.KosTestSupport
import nct.engineering.modern.commons.KosTestSupport.Companion.awaitForResult
import nct.engineering.modern.pg.PostgresClient
import java.util.*

class MarketListingInvalidRecordsRemoverTest : DescribeSpec({

    val kosTestSupport = KosTestSupport()
    val db = kosTestSupport.fetchInstance<PostgresClient>()
    val objectMapper = kosTestSupport.fetchInstance<ObjectMapper>()
    val remover = MarketListingInvalidRecordsRemover(db)

    afterEach {
        db.withConnection { conn ->
            conn.executeQuery("DELETE FROM data_api_v2.historical_data").compose {
                conn.executeQuery("ALTER SEQUENCE data_api_v2.historical_data_row_numbers RESTART WITH 0")
            }
        }.awaitForResult()
    }

    afterSpec { db.close()}

    val defaultKeyRanges = KeyParser.parseKeyRange(1)

    describe("when removing invalid market listing records") {

        describe("when there are no records") {
            it("should complete successfully with 0 deletions") {
                defaultKeyRanges.map {
                    remover.execute(
                        PartitioningMigrationSubTaskDef(
                            taskName = ARC8278_MIGRATION_TASK_NAME,
                            organisationName = "raywhite",
                            domainComponentName = "marketlisting",
                            keyRange = it
                        )
                    ).awaitForResult()
                }
                // Test passes if no exception is thrown
            }
        }

        describe("when there are valid records (with both lastTouched and suburbId)") {
            val validId1 = UUID.randomUUID().toString()
            val validId2 = UUID.randomUUID().toString()

            beforeEach {
                insertTestRecord(
                    db,
                    objectMapper,
                    validId1,
                    """{"id": "$validId1", "lastTouched": "2023-01-01T00:00:00Z", "suburbId": "12345"}"""
                ).awaitForResult()
                insertTestRecord(
                    db,
                    objectMapper,
                    validId2,
                    """{"id": "$validId2", "lastTouched": "2023-01-02T00:00:00Z", "suburbId": "67890"}"""
                ).awaitForResult()
            }

            it("should not delete valid records") {
                defaultKeyRanges.map {
                    remover.execute(
                        PartitioningMigrationSubTaskDef(
                            taskName = ARC8278_MIGRATION_TASK_NAME,
                            organisationName = "raywhite",
                            domainComponentName = "marketlisting",
                            keyRange = it
                        )
                    ).awaitForResult()
                }

                val remainingCount = countMarketListingRecords(db).awaitForResult()
                remainingCount shouldBe 2
            }
        }

        describe("when there are invalid records (missing lastTouched or suburbId)") {
            val invalidId1 = UUID.randomUUID().toString()
            val invalidId2 = UUID.randomUUID().toString()
            val invalidId3 = UUID.randomUUID().toString()
            val invalidId4 = UUID.randomUUID().toString()

            beforeEach {
                // Missing both lastTouched and suburbId
                insertTestRecord(db, objectMapper, invalidId1, """{"id": "$invalidId1", "name": "test"}""").awaitForResult()
                // Missing lastTouched only
                insertTestRecord(
                    db,
                    objectMapper,
                    invalidId2,
                    """{"id": "$invalidId2", "description": "test", "suburbId": "12345"}"""
                ).awaitForResult()
                // Missing suburbId only
                insertTestRecord(
                    db,
                    objectMapper,
                    invalidId3,
                    """{"id": "$invalidId3", "lastTouched": "2023-01-01T00:00:00Z", "name": "test"}"""
                ).awaitForResult()
                // Missing both attributes but with other data
                insertTestRecord(db, objectMapper, invalidId4, """{"id": "$invalidId4", "property": "value"}""").awaitForResult()
            }

            it("should delete invalid records") {
                defaultKeyRanges.map {
                    remover.execute(
                        PartitioningMigrationSubTaskDef(
                            taskName = ARC8278_MIGRATION_TASK_NAME,
                            organisationName = "raywhite",
                            domainComponentName = "marketlisting",
                            keyRange = it
                        )
                    ).awaitForResult()
                }

                val remainingCount = countMarketListingRecords(db).awaitForResult()
                remainingCount shouldBe 0
            }
        }

        describe("when there are mixed valid and invalid records") {
            val validId1 = UUID.randomUUID().toString()
            val validId2 = UUID.randomUUID().toString()
            val invalidId1 = UUID.randomUUID().toString()
            val invalidId2 = UUID.randomUUID().toString()
            val invalidId3 = UUID.randomUUID().toString()

            beforeEach {
                // Valid records with both attributes
                insertTestRecord(
                    db,
                    objectMapper,
                    validId1,
                    """{"id": "$validId1", "lastTouched": "2023-01-01T00:00:00Z", "suburbId": "12345"}"""
                ).awaitForResult()
                insertTestRecord(
                    db,
                    objectMapper,
                    validId2,
                    """{"id": "$validId2", "lastTouched": "2023-01-02T00:00:00Z", "suburbId": "67890"}"""
                ).awaitForResult()
                // Invalid records missing one or both attributes
                insertTestRecord(db, objectMapper, invalidId1, """{"id": "$invalidId1", "name": "test"}""").awaitForResult()
                insertTestRecord(
                    db,
                    objectMapper,
                    invalidId2,
                    """{"id": "$invalidId2", "description": "test", "suburbId": "11111"}"""
                ).awaitForResult()
                insertTestRecord(
                    db,
                    objectMapper,
                    invalidId3,
                    """{"id": "$invalidId3", "lastTouched": "2023-01-03T00:00:00Z", "name": "test"}"""
                ).awaitForResult()
            }

            it("should only delete invalid records") {
                defaultKeyRanges.map {
                    remover.execute(
                        PartitioningMigrationSubTaskDef(
                            taskName = ARC8278_MIGRATION_TASK_NAME,
                            organisationName = "raywhite",
                            domainComponentName = "marketlisting",
                            keyRange = it
                        )
                    ).awaitForResult()
                }

                val remainingCount = countMarketListingRecords(db).awaitForResult()
                remainingCount shouldBe 2

                val remainingRecords = getMarketListingRecords(db).awaitForResult()
                val remainingIds = remainingRecords!!.map { it.getUUID("id").toString() }.sorted()
                remainingIds shouldBe listOf(validId1, validId2).sorted()
            }
        }

        describe("when testing specific attribute combinations") {
            val validBothId = UUID.randomUUID().toString()
            val invalidMissingLastTouchedId = UUID.randomUUID().toString()
            val invalidMissingSuburbId = UUID.randomUUID().toString()
            val invalidMissingBothId = UUID.randomUUID().toString()

            beforeEach {
                // Valid: has both lastTouched and suburbId
                insertTestRecord(
                    db,
                    objectMapper,
                    validBothId,
                    """{"id": "$validBothId", "lastTouched": "2023-01-01T00:00:00Z", "suburbId": "12345", "name": "valid"}"""
                ).awaitForResult()
                // Invalid: missing lastTouched
                insertTestRecord(
                    db,
                    objectMapper,
                    invalidMissingLastTouchedId,
                    """{"id": "$invalidMissingLastTouchedId", "suburbId": "67890", "name": "invalid1"}"""
                ).awaitForResult()
                // Invalid: missing suburbId
                insertTestRecord(
                    db,
                    objectMapper,
                    invalidMissingSuburbId,
                    """{"id": "$invalidMissingSuburbId", "lastTouched": "2023-01-02T00:00:00Z", "name": "invalid2"}"""
                ).awaitForResult()
                // Invalid: missing both
                insertTestRecord(
                    db,
                    objectMapper,
                    invalidMissingBothId,
                    """{"id": "$invalidMissingBothId", "name": "invalid3"}"""
                ).awaitForResult()
            }

            it("should only keep records with both lastTouched and suburbId") {
                defaultKeyRanges.map {
                    remover.execute(
                        PartitioningMigrationSubTaskDef(
                            taskName = ARC8278_MIGRATION_TASK_NAME,
                            organisationName = "raywhite",
                            domainComponentName = "marketlisting",
                            keyRange = it
                        )
                    ).awaitForResult()
                }

                val remainingCount = countMarketListingRecords(db).awaitForResult()
                remainingCount shouldBe 1

                val remainingRecords = getMarketListingRecords(db).awaitForResult()
                val remainingIds = remainingRecords!!.map { it.getUUID("id").toString() }
                remainingIds shouldBe listOf(validBothId)
            }
        }
    }
})

private fun insertTestRecord(db: PostgresClient, objectMapper: ObjectMapper, id: String, payloadJson: String) =
    db.withConnection { conn ->
        val insertQuery = """
            INSERT INTO data_api_v2.historical_data
            (org_name, business_id, agency_id, id, component_name, payload, is_deleted, last_touched, delete_is_merge, merge_payload)
            VALUES ('raywhite', '00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000', '$id', 'marketlisting', '$payloadJson', false, CURRENT_TIMESTAMP, false, null)
        """.trimIndent()
        conn.executeQuery(insertQuery)
    }

private fun countMarketListingRecords(db: PostgresClient) =
    db.withConnection { conn ->
        conn.executeQuery("SELECT COUNT(*) as count FROM data_api_v2.historical_data WHERE org_name = 'raywhite' AND component_name = 'marketlisting'")
            .map { it.first().getInteger("count") }
    }

private fun getMarketListingRecords(db: PostgresClient) =
    db.withConnection { conn ->
        conn.executeQuery("SELECT id FROM data_api_v2.historical_data WHERE org_name = 'raywhite' AND component_name = 'marketlisting' ORDER BY id")
    }
