terraform {
  backend "remote" {
    organization = "nurturecloud"
  }
  required_providers {
    google = {
      version = "~> 6.38.0"
    }
    google-beta = {
      version = "~> 6.38.0"
    }
    postgresql = {
      source  = "cyrilgdn/postgresql"
      version = "~> 1.25.0"
    }
  }
}

provider "google" {
  project = var.gcp_project_id
  region  = var.gcp_region
  default_labels = {
    service = "dapi"
    env = var.deployment_environment
  }
  add_terraform_attribution_label               = true
  terraform_attribution_label_addition_strategy = "PROACTIVE"
}

provider "google-beta" {
  project = var.gcp_project_id
  region  = var.gcp_region
  default_labels = {
    service = "dapi"
    env = var.deployment_environment
  }
  add_terraform_attribution_label               = true
  terraform_attribution_label_addition_strategy = "PROACTIVE"
}
