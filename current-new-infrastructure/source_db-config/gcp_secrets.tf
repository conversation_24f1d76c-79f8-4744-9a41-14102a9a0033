data "google_secret_manager_secret_version" "dapi_db_password" {
  secret = "${local.application_name}-db-password"
}

data "google_secret_manager_secret_version" "flyway_db_password" {
  secret = "${local.application_name}-flyway-db-password"
}

data "google_secret_manager_secret_version" "db_admin_db_password" {
  secret = "${local.application_name}-db_admin-db-password"
}

data "google_secret_manager_secret_version" "db_username2" {
  secret = "${local.application_name}-db-username2"
}

data "google_secret_manager_secret_version" "db_password2" {
  secret = "${local.application_name}-db-password2"
}

data "google_secret_manager_secret_version" "metabase_db_password" {
  secret = "${local.application_name}-metabase-db-password"
}

data "google_secret_manager_secret_version" "operator_ro_db_password" {
  secret = "${local.application_name}-operator-ro-db-password"
}

data "google_secret_manager_secret_version" "operator_rw_db_password" {
  secret = "${local.application_name}-operator-rw-db-password"
}
