locals {
  schemas = toset([
    "data_api",
    "data_api_v2",
  ])
}

module "database_permissions" {
  source  = "app.terraform.io/nurturecloud/permissions/postgresql"
  version = "0.6.0"

  psql_port         = var.tf_provider_postgres_port
  database_name     = "data-stable"
  database_user     = "db_admin"
  db_admin_password = data.google_secret_manager_secret_version.db_admin_db_password.secret_data
  sslmode           = "disable"
  managed_schemas   = []
  managed_users = [
    {
      name                    = "dapi"
      password                = data.google_secret_manager_secret_version.dapi_db_password.secret_data
      role                    = "read_write"
      statement_timeout_in_ms = 500
    },
    {
      name                    = "flyway"
      password                = data.google_secret_manager_secret_version.flyway_db_password.secret_data
      role                    = "schema_admin"
      statement_timeout_in_ms = 10000
    },
    {
      name                    = "metabase"
      password                = data.google_secret_manager_secret_version.metabase_db_password.secret_data
      role                    = "read_only"
      statement_timeout_in_ms = 2500
    },
    {
      name                    = "operator_ro"
      password                = data.google_secret_manager_secret_version.operator_ro_db_password.secret_data
      role                    = "read_only"
      statement_timeout_in_ms = 2000
    },
    {
      name                    = "operator_rw"
      password                = data.google_secret_manager_secret_version.operator_rw_db_password.secret_data
      role                    = "read_write"
      statement_timeout_in_ms = 2000
    },
  ]
  unmanaged_schemas = local.schemas
}
