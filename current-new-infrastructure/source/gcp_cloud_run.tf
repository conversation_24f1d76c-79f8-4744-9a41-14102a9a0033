locals {
  number_of_instances = 1
  connections_allocated_for_admin_purposes = 5

  # Computes the number of connections that should be allocated per instance.
  # It tries to allocate as many connections as possible for the application
  # whilst preserving a few for admin purposes.
  #
  # Note: we are consuming up to 75% of the connections to leave room for
  # smoother deployments. Otherwise during rollout, given it is constantly probing
  # for connections (including for Health Check) it might take longer for DAPI to stabilise.
  max_db_connections_per_instance = floor(local.max_db_connections / local.number_of_instances * 0.75) - local.connections_allocated_for_admin_purposes
}

module "cloud-run" {
  source  = "app.terraform.io/nurturecloud/cloud-run/gcp"
  version = "3.1.2"

  application_name     = local.application_name
  managed_domain       = data.google_dns_managed_zone.this.dns_name
  managed_zone         = data.google_dns_managed_zone.this.name
  service_account_name = local.application_name

  # Use the default Serverless VPC Connector pre-created for this deployment environment
  vpc_connector_name = local.infrastructure_project_name

  # Auto Scaling Config
  auto_scaling = {
    min_instances: local.number_of_instances
    max_instances: local.number_of_instances
    max_concurrent_requests: 500
  }

  # Computing Resources
  computing_resources = {
    cpu: "2000m"
    memory: "2048Mi"
    cpu_always_allocated: true
  }

  liveness_probe = {
    failure_threshold: 3,
    initial_delay_seconds: 10,
    timeout_seconds: 3,
    period_seconds: 10,
    http_get: {
      path: "/public/health-check",
    }
  }

  # Environment Variables that will be accessible within the service
  environment_variables = {
    "GCP_APP_NAME": local.application_name
    "GCP_PROJECT_ID": var.gcp_project_id
    "LOG_LEVEL": "INFO"

    "TOPIC_EVENT_NORMALISED_DATA"=module.managed_topics.topics["event-webhook-notification"]
    "TOPIC_EVENT_NORMALISED_DATA_TARGETED"=module.managed_topics.topics["event-webhook-notification-targeted"]
    "TOPIC_TASK_WEBHOOK_NOTIFICATION_TO_TARGET_URL"=module.managed_topics.topics["task-webhook-notification-to-target"]
    "TOPIC_EVENT_MODIFICATIONS_SOURCE_OF_TRUTH"=module.managed_topics.topics["event-sot-modifications"]
    "TOPIC_EVENT_INGESTION_STATE_ON_SOURCE_OF_TRUTH"=module.managed_topics.topics["event-sot-ingestion-finished"]
    "TOPIC_TASK_UPSERT_FROM_SOURCE_OF_TRUTH"=module.managed_topics.topics["task-sot-modifications-upserts"]
    "TOPIC_TASK_FETCH_ALL_PAGES"=module.managed_topics.topics["task-fetch-all-pages"]
    "TOPIC_TASK_FETCH_SINGLE_PAGE"=module.managed_topics.topics["task-fetch-single-page"]
    "TOPIC_TASK_FETCH_BY_ID"=module.managed_topics.topics["task-fetch-by-id"]
    "TOPIC_TASK_UPSERT_TO_SOURCE_OF_TRUTH"=module.managed_topics.topics["task-upsert-modifications-to-sot"]
    "TOPIC_TASK_INGESTION_RELAY"=module.managed_topics.topics["task-ingestion-relay"]
    "TOPIC_TASK_MIGRATION"=module.managed_topics.topics["task-migration"]
    "TOPIC_TASK_MIGRATION_BATCH"=module.managed_topics.topics["task-migration-batch"]

    "REDIS_HOST": google_redis_instance.cache.host
    "REDIS_PORT": google_redis_instance.cache.port
    "PSQL_HOST": google_sql_database_instance.default.public_ip_address
    "PSQL_PORT": 5432
    "PSQL_POOL_MAX_CONNECTIONS": local.max_db_connections_per_instance
    "PSQL_DBNAME": google_sql_database.default.name

    # Feature NOTIFICATION_SUBSCRIPTIONS_CACHE_MISSES
    "FEATURE_FETCH_NOTIFICATION_SUBSCRIPTIONS_CACHE_MISSES_EXECUTION_STRATEGY": "AS_IS"

    # Feature FETCH_BROADCAST_SUBSCRIPTIONS
    "FEATURE_FETCH_BROADCAST_SUBSCRIPTIONS_EXECUTION_STRATEGY": "CIRCUIT_BREAKER"
    "FEATURE_FETCH_BROADCAST_SUBSCRIPTIONS_TIMEOUT": 10000
    "FEATURE_FETCH_BROADCAST_SUBSCRIPTIONS_RESET_TIMEOUT": 60000
    "FEATURE_FETCH_BROADCAST_SUBSCRIPTIONS_MAX_FAILURES": 10

    # Feature SEND_WEBHOOK_NOTIFICATION
    "FEATURE_SEND_WEBHOOK_NOTIFICATION_EXECUTION_STRATEGY": "CIRCUIT_BREAKER"
    "FEATURE_SEND_WEBHOOK_NOTIFICATION_TIMEOUT": 20000
    "FEATURE_SEND_WEBHOOK_NOTIFICATION_RESET_TIMEOUT": 60000
    "FEATURE_SEND_WEBHOOK_NOTIFICATION_MAX_FAILURES": 10
    "FEATURE_SEND_WEBHOOK_NOTIFICATION_MAX_RETRIES": 2

    # Feature PUBLISH_WEBHOOK_NOTIFICATION
    "FEATURE_PUBLISH_WEBHOOK_NOTIFICATION_TIMEOUT": 10000

    # Feature REPLICATION_PERSISTENCE
#    "FEATURE_REPLICATION_PERSISTENCE_ENABLED": true

    "FEATURE_PERSIST_HISTORICAL_DATA_FROM_DLQ_ENABLED": false
  }

  # Environment Variables containing secrets from SecretsManager
  secret_environment_variables = {
    "PSQL_USERNAME": google_secret_manager_secret.db_username2.secret_id
    "PSQL_PASSWORD": google_secret_manager_secret.db_password2.secret_id
    "API_MGNT_USERNAME": google_secret_manager_secret.api_management_username.secret_id
    "API_MGNT_PASSWORD": google_secret_manager_secret.api_management_password.secret_id
    "INTERNAL_SERVICE_TOKEN": data.google_secret_manager_secret_version.global_internal_service_token.secret
  }

  depends_on = [
    google_secret_manager_secret.db_username2,
    google_secret_manager_secret.db_password2,
    google_secret_manager_secret.api_management_username,
    google_secret_manager_secret.api_management_password,
  ]
}

data "google_dns_managed_zone" "this" {
  name = var.infrastructure_managed_domain
}
