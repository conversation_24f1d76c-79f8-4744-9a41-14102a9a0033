locals {
  max_db_connections = 200
}

resource "random_password" "db_username2" {
  length  = 16
  special = false
}

resource "random_password" "db_password2" {
  length  = 64
  special = false
}

locals {
  authorized_ips = [
    "${data.google_compute_address.nat.address}/32"
  ]
}

resource "google_sql_database_instance" "default" {
  name             = local.application_name
  database_version = "POSTGRES_14"

  settings {
    tier = "db-custom-4-16384"

    deletion_protection_enabled = true

    insights_config {
      query_insights_enabled  = true
      record_application_tags = true
    }

    database_flags {
      name  = "max_connections"
      value = local.max_db_connections
    }

    database_flags {
      name  = "effective_cache_size"
      value = "688652"
    }

    database_flags {
      name  = "shared_buffers"
      value = "275460"
    }

    database_flags {
      name  = "work_mem"
      value = "1375"
    }

    database_flags {
      name  = "maintenance_work_mem"
      value = "275458"
    }

    ip_configuration {
      private_network = data.google_compute_network.default.id

      dynamic "authorized_networks" {
        for_each = local.authorized_ips
        iterator = ips

        content {
          name  = "Authorised IP #${ips.key}"
          value = ips.value
        }
      }
    }
  }
}

resource "google_sql_user" "db_admin" {
  instance = google_sql_database_instance.default.name
  name     = "db_admin"
  password = google_secret_manager_secret_version.db_admin_db_password.secret_data
}

resource "google_sql_database" "default" {
  instance = google_sql_database_instance.default.name
  name     = "data-stable"
}
