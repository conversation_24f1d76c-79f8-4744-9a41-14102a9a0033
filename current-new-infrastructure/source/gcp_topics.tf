module "managed_topics" {
  source  = "app.terraform.io/nurturecloud/pubsub-managed-topics/gcp"
  version = "7.2.4"

  application_name = local.application_name

  topic_using_default_subscription = [
    "event-sot-modifications",
    "task-sot-modifications-upserts",
    "task-webhook-notification-to-target",
    "task-fetch-all-pages",
    "task-fetch-single-page",
    "task-fetch-by-id",
    "task-upsert-modifications-to-sot",
    "task-ingestion-relay",
    "task-migration",
    "task-migration-batch",
    "event-sot-ingestion-finished",
    "event-webhook-notification-targeted"
  ]

  topics_and_subscriptions = {
    "event-webhook-notification": [
      {
        name: "persistence"
        filter: "NOT attributes.state = \"Receipt\""
        minimum_backoff: 300
        maximum_backoff: 600
        max_delivery_attempts: 20
      },
    ]
  }

  default_subscription_configuration = {
    ack_deadline_seconds: 600
  }

  monitoring_channel_names = module.microservice-monitoring-base.monitoring_channel_names
}
