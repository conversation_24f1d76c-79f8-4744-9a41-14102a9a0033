resource "google_secret_manager_secret" "dapi_db_password" {
  secret_id = "${local.application_name}-db-password"
  replication {
    auto {}
  }
}

resource "random_password" "dapi_db_password" {
  length  = 64
  special = false
}

resource "google_secret_manager_secret_version" "dapi_db_password" {
  secret         = google_secret_manager_secret.dapi_db_password.id
  secret_data_wo = random_password.dapi_db_password.result
}

# flyway password
resource "google_secret_manager_secret" "flyway_db_password" {
  secret_id = "${local.application_name}-flyway-db-password"
  replication {
    auto {}
  }
}

resource "random_password" "flyway_db_password" {
  length  = 64
  special = false
}

resource "google_secret_manager_secret_version" "flyway_db_password" {
  secret         = google_secret_manager_secret.flyway_db_password.id
  secret_data_wo = random_password.flyway_db_password.result
}

resource "google_secret_manager_secret" "db_admin_db_password" {
  secret_id = "${local.application_name}-db_admin-db-password"
  replication {
    auto {}
  }
}

resource "random_password" "db_admin_db_password" {
  length  = 64
  special = false
}

resource "google_secret_manager_secret_version" "db_admin_db_password" {
  secret      = google_secret_manager_secret.db_admin_db_password.id
  secret_data = random_password.db_admin_db_password.result
}

resource "google_secret_manager_secret" "db_username2" {
  secret_id = "${local.application_name}-db-username2"
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "db_username2" {
  secret      = google_secret_manager_secret.db_username2.id
  secret_data = random_password.db_username2.result
}

# Database Password
resource "google_secret_manager_secret" "db_password2" {
  secret_id = "${local.application_name}-db-password2"
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "db_password2" {
  secret      = google_secret_manager_secret.db_password2.id
  secret_data = random_password.db_password2.result
}
# API Management Username
resource "random_password" "api_management_username" {
  length  = 16
  special = false
}

resource "google_secret_manager_secret" "api_management_username" {
  secret_id = "${local.application_name}-api-management-username"
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "api_management_username" {
  secret      = google_secret_manager_secret.api_management_username.id
  secret_data = random_password.api_management_username.result
}

# API Management Password
resource "random_password" "api_management_password" {
  length  = 32
  special = false
}

resource "google_secret_manager_secret" "api_management_password" {
  secret_id = "${local.application_name}-api-management-password"
  replication {
    auto {}
  }
}

resource "google_secret_manager_secret_version" "api_management_password" {
  secret      = google_secret_manager_secret.api_management_password.id
  secret_data = random_password.api_management_password.result
}

# Global Internal Service Token
data "google_secret_manager_secret_version" "global_internal_service_token" {
  secret = "global_internal_service_token"
}

resource "google_secret_manager_secret" "metabase" {
  secret_id = "${local.application_name}-metabase-db-password"
  replication {
    auto {}
  }
}

resource "random_password" "metabase" {
  length  = 64
  special = false
}

resource "google_secret_manager_secret_version" "metabase" {
  secret      = google_secret_manager_secret.metabase.id
  secret_data = random_password.metabase.result
}

resource "google_secret_manager_secret" "operator_ro" {
  secret_id = "${local.application_name}-operator-ro-db-password"
  replication {
    auto {}
  }
}

resource "random_password" "operator_ro" {
  length  = 64
  special = false
}

resource "google_secret_manager_secret_version" "operator_ro" {
  secret      = google_secret_manager_secret.operator_ro.id
  secret_data = random_password.operator_ro.result
}

resource "google_secret_manager_secret" "operator_rw" {
  secret_id = "${local.application_name}-operator-rw-db-password"
  replication {
    auto {}
  }
}

resource "random_password" "operator_rw" {
  length  = 64
  special = false
}

resource "google_secret_manager_secret_version" "operator_rw" {
  secret      = google_secret_manager_secret.operator_rw.id
  secret_data = random_password.operator_rw.result
}
