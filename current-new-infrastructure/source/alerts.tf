locals {
  notification_persistence_queue = "${module.managed_topics.topics["event-webhook-notification"]}-persistence"
}

resource "google_monitoring_alert_policy" "replication_persistence_queue" {
  display_name = "Replica Persistence Queue"
  combiner     = "AND"
  notification_channels = module.microservice-monitoring-base.monitoring_channel_names
  conditions {
    display_name = "Replica Persistence Queue"
    condition_prometheus_query_language {
      query = "avg(avg_over_time(custom_googleapis_com:feature_persist_historical_data_v2_timer{namespace=\"${module.cloud-run.cloud_run_service_name}\", monitored_resource=\"generic_node\"}[5m])) > 400"
      duration = "300s"
      evaluation_interval = "60s"
    }
  }

  documentation {
    content = <<-EOT
            - **Reason**: <PERSON><PERSON> is struggling to ingest data.
            - **Affected Component**: Dapi Ingestion Pipeline
            EOT
    mime_type = "text/markdown"
  }

  depends_on = [
    module.managed_topics
  ]
}

resource "google_monitoring_alert_policy" "replication_persistence_queue_unacked_age" {
  display_name = "DAPI ${var.deployment_environment}'s Replica Persistence Queue Un-acked Message Age"
  combiner     = "AND"
  notification_channels = module.microservice-monitoring-base.monitoring_channel_names

  conditions {
    display_name = "Replica Persistence Queue Un-acked Message Age"
    condition_prometheus_query_language {
      query      = "max_over_time(pubsub_googleapis_com:subscription_oldest_unacked_message_age{monitored_resource='pubsub_subscription',subscription_id='${local.notification_persistence_queue}'}[5m]) > 2100"
      duration   = "60s"
      evaluation_interval = "60s"
    }
  }

  alert_strategy {
    auto_close = "604800s"
  }

  documentation {
    content = "- `reason`: Dapi persistence queue has tail latency greater than 35m\n- `affected component`: Persistence\n- `recommended action`: May be a spike resulting from rebootstrap, however, it is recommended to check the logs"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "replication_persistence_processing_time" {
  display_name = "Replica Persistence Processing Time"
  combiner     = "AND"
  notification_channels = module.microservice-monitoring-base.monitoring_channel_names
  conditions {
    display_name = "Replica Persistence Processing Time"
    condition_threshold {
      filter = "metric.type=\"pubsub.googleapis.com/subscription/num_undelivered_messages\" resource.type=\"pubsub_subscription\" resource.labels.subscription_id=\"${local.application_name}-event-webhook-notification-persistence\""
      duration = "1800s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period = "300s"
        per_series_aligner = "ALIGN_SUM"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields = ["resource.labels.subscription_id"]
      }
      threshold_value = 1000000
      trigger {
        count = 1
      }
    }
  }

  alert_strategy {
    auto_close = "604800s"
  }

  documentation {
    content = <<-EOT
            - **Reason**: Dapi is struggling to ingest data.
            - **Affected Component**: Dapi Ingestion Pipeline
            EOT
    mime_type = "text/markdown"
  }

  depends_on = [
    module.managed_topics
  ]
}

resource "google_monitoring_alert_policy" "unhandled_exceptions" {
  display_name = "DAPI ${var.deployment_environment}'s Unhandled Exceptions"
  combiner     = "AND"
  notification_channels = module.microservice-monitoring-base.monitoring_channel_names

  conditions {
    display_name = "Number of Unhandled Exceptions"
    condition_prometheus_query_language {
      query      = "sum(sum_over_time(custom_googleapis_com:dapi_unhandled_exceptions{monitored_resource='generic_node',namespace='${local.application_name}'}[5m])) > 50"
      duration   = "60s"
      evaluation_interval = "60s"
    }
  }

  alert_strategy {
    auto_close = "604800s"
  }

  documentation {
    content = join("\n", [
      "- `reason`: Dapi have more unhandled exceptions than expected",
      "- `affected component`: System wide",
      "## Recommended action",
      "- check the logs for warnings/errors",
      "- go to Metrics Explorer and look for the custom metric 'dapi/unhandled_exceptions'. You can use 'origin' and 'cause' tags to better understand what's going on."
    ])
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "errors_on_dapi_logs" {
  display_name = "DAPI ${var.deployment_environment} - Errors on Logs"
  combiner = "OR"
  notification_channels = module.microservice-monitoring-base.monitoring_channel_names
  conditions {
    display_name = "Errors on logs"
    condition_threshold {
      aggregations {
        alignment_period = "60s"
        cross_series_reducer = "REDUCE_SUM"
        per_series_aligner = "ALIGN_SUM"
      }
      comparison = "COMPARISON_GT"
      duration = "0s"
      filter = "resource.type=\"cloud_run_revision\" metric.type=\"logging.googleapis.com/user/logs/errors/counter\" metric.label.\"namespace\"=\"${local.application_name}\""
      threshold_value = 100
      trigger {
        count = 1
      }
    }
  }
  alert_strategy {
    auto_close = "604800s"
  }
  documentation {
    content = "- `reason`: Dapi is printing more `error` logs than expected\n- `affected component`: N/A\n- `recommended action`: Check the logs"
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "database_cpu" {
  display_name = "DAPI ${var.deployment_environment} - Database CPU"
  combiner     = "OR"
  notification_channels = module.microservice-monitoring-base.monitoring_channel_names
  conditions {
    display_name = "Database CPU"
    condition_threshold {
      filter = "resource.type = \"cloudsql_database\" AND metric.type = \"cloudsql.googleapis.com/database/cpu/utilization\" AND metadata.system_labels.name = \"${local.application_name}\""
      duration = "900s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period = "60s"
        per_series_aligner = "ALIGN_MEAN"
        cross_series_reducer = "REDUCE_PERCENTILE_99"
        group_by_fields = ["resource.labels.database_id"]
      }
      threshold_value = 0.6
      trigger {
        count = 1
      }
    }
  }
  alert_strategy {
    auto_close = "604800s"
  }
  documentation {
    content = "- `reason`: Dapi's main database is (apparently) overwhelmed\n- `affected components`: Fetch And Relay; Ingestion Persistence\n- `recommended action`: Raise that to the platform team (this is a temporary measure until we create a troubleshooting guide)"
    mime_type = "text/markdown"
  }
}

module "microservice-monitoring-base" {
  source  = "app.terraform.io/nurturecloud/microservice-monitoring-base/nct"
  version = "1.0.2"

  cloud_run_services = [
    {
      cloud_run_service_name = local.application_name
      location = var.gcp_region
    },
  ]

  deployment_environment = var.infrastructure_deployment_environment
  new_infra = true
}

