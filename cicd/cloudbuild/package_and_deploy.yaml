logsBucket: 'gs://$PROJECT_ID-build-logs'

substitutions:
  _APP_NAME: 'data'
  _DEPLOYMENT_ENVIRONMENT: 'unstable'
  _IMAGE_DEPLOYMENT_ENVIRONMENT: 'dev'
  _GCP_REGION: 'southeast-australia1'

options:
  dynamic_substitutions: true
  machineType: 'E2_HIGHCPU_32'

  # Do not include here the environment variables you want to be injected
  # into the application. Instead, use Google Runtime Configurator.
  # For more details, check: docs/env_vars.md
  env:
    - GCP_REGION=$_GCP_REGION
    - GCP_PROJECT_ID=$PROJECT_ID

steps:
  # Generates the application's fat-jar
  - name: '$_GCP_REGION-docker.pkg.dev/$PROJECT_ID/new-infrastructure-$_IMAGE_DEPLOYMENT_ENVIRONMENT-docker-apps/$_APP_NAME-builder-$_DEPLOYMENT_ENVIRONMENT'
    entrypoint: "./cicd/build_jar.sh"

  - name: '$_GCP_REGION-docker.pkg.dev/$PROJECT_ID/new-infrastructure-$_IMAGE_DEPLOYMENT_ENVIRONMENT-docker-apps/$_APP_NAME-builder-$_DEPLOYMENT_ENVIRONMENT'
    entrypoint: "./cicd/run_integration_tests.sh"

  # Applies the migration
  - name: '$_GCP_REGION-docker.pkg.dev/$PROJECT_ID/new-infrastructure-$_IMAGE_DEPLOYMENT_ENVIRONMENT-docker-apps/$_APP_NAME-builder-$_DEPLOYMENT_ENVIRONMENT'
    entrypoint: "./cicd/flyway.sh"
    env:
      - PSQL_HOST=127.0.0.1
      - PSQL_PORT=5432
      - PSQL_USERNAME=flyway
      - PSQL_DBNAME=${_APP_NAME}-stable
      - INSTANCE_CONNECTION_NAME=${PROJECT_ID}:${_GCP_REGION}:${_APP_NAME}-${_DEPLOYMENT_ENVIRONMENT}
    secretEnv:
      - PSQL_PASSWORD

  # Wraps the application into a Docker container
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - '$_GCP_REGION-docker.pkg.dev/$PROJECT_ID/new-infrastructure-$_IMAGE_DEPLOYMENT_ENVIRONMENT-docker-apps/$_APP_NAME-$_DEPLOYMENT_ENVIRONMENT:$COMMIT_SHA'
      - '.'

  # Publishes the Docker image to the Google Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', '$_GCP_REGION-docker.pkg.dev/$PROJECT_ID/new-infrastructure-$_IMAGE_DEPLOYMENT_ENVIRONMENT-docker-apps/$_APP_NAME-$_DEPLOYMENT_ENVIRONMENT:$COMMIT_SHA']

  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - run
      - deploy
      - $_APP_NAME-$_DEPLOYMENT_ENVIRONMENT
      - --region
      - $_GCP_REGION
      - --image
      - $_GCP_REGION-docker.pkg.dev/$PROJECT_ID/new-infrastructure-$_IMAGE_DEPLOYMENT_ENVIRONMENT-docker-apps/$_APP_NAME-$_DEPLOYMENT_ENVIRONMENT:$COMMIT_SHA

availableSecrets:
  secretManager:
    - versionName: projects/$PROJECT_ID/secrets/${_APP_NAME}-${_DEPLOYMENT_ENVIRONMENT}-flyway-db-password/versions/latest
      env: PSQL_PASSWORD
